"""
Project slide module.
Responsible for rendering the "Notre projet" (Our Project) section of the pitch deck.
"""

from pptx.util import <PERSON><PERSON>, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from src.gemini_api import generate_content
from src.Template_1.text_utils import wrap_text

def add_project_slide(prs, data):
    """
    Adds the 'Notre projet' (Our Project) content to the appropriate slide in the presentation.

    This function generates a concise and impactful description of the project's objective
    using Google's Gemini AI, formats it for readability, and then adds it to the
    project slide in the PowerPoint presentation, along with a title.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, used as input for AI content generation.
    """
    # --- Generate Project Description with Gemini AI ---
    # Define the prompt for Gemini AI to generate a concise project objective paragraph.
    # The prompt specifies a 4-sentence structure, maximum 70 words, and an engaging tone.
    prompt_projet = (
        "En tant qu'expert en rédaction de pitch decks, rédige un paragraphe concise mettant en avant l'objectif du projet. "
        f"Utilise les informations suivantes : {data}. "
        "Fais en sorte que le texte soit bien structuré en 4 phrases courtes ne depasse pas 70 mots. "
        "Utilise un ton engageant et informatif pour convaincre les parties prenantes de son potentiel. "
        "Avec des retours à la ligne pour une meilleure lisibilité."
    )
    # Generate content using the Gemini API.
    projet_raw = generate_content(prompt_projet)
    # Wrap the generated text to ensure it fits within the slide's layout, with a line limit of 8.
    projet_text = wrap_text(projet_raw, 8)

    # Access the specific slide intended for the project description.
    # In this template, it's assumed to be the 7th slide (index 6).
    slide = prs.slides[6]

    # --- Add the Title "Notre projet" to the Slide ---
    # Define the position and size for the title textbox.
    left, top, width, height = Inches(5), Inches(1), Inches(4), Inches(1)
    # Add the textbox shape for the title.
    title_box = slide.shapes.add_textbox(left, top, width, height)
    # Get the text frame of the title textbox.
    title_frame = title_box.text_frame
    # Set the title text.
    title_frame.text = "Notre projet"
    # Apply font formatting to all paragraphs and runs within the title text frame.
    for paragraph in title_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(40)  # Set font size.
            run.font.bold = True    # Set font to bold.
            run.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.

    # --- Add the Project Content under the Title ---
    # Define the position and size for the content textbox.
    left, top, width, height = Inches(2.5), Inches(2), Inches(6), Inches(3)
    # Add the textbox shape for the content.
    content_box = slide.shapes.add_textbox(left, top, width, height)
    # Get the text frame of the content textbox.
    content_frame = content_box.text_frame
    # Set the project content.
    content_frame.text = projet_text
    # Apply font formatting and alignment to all paragraphs and runs within the content text frame.
    for paragraph in content_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(15)  # Set font size.
            run.font.bold = False   # Set font to not bold.
            run.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.
        paragraph.alignment = PP_ALIGN.JUSTIFY # Justify align the text.
