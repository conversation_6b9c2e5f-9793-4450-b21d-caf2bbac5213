

"""
Thanks slide module.
Responsible for rendering the "Thank you" (closing) section of the pitch deck.
"""

from pptx.util import <PERSON><PERSON>, Pt
from pptx.dml.color import RGBColor

def add_thanks_slide(prs, data):
    """
    Adds the closing/thanks content to the appropriate slide in the presentation.

    This function populates the final slide of the presentation with a "Thank you"
    message and contact information (email, phone, website, social media links)
    retrieved from the `data` dictionary.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, containing contact and social media information.
    """
    # Access the specific slide intended for the "Thank you" message.
    # In this template, it's assumed to be the 16th slide (index 15).
    slide = prs.slides[15]

    # --- Add the Title "Merci pour votre attention" ---
    # Define the position and size for the title textbox.
    left_title, top_title, width_title, height_title = Inches(1), Inches(1), Inches(6), Inches(1)
    # Add the textbox shape for the title.
    text_box_title = slide.shapes.add_textbox(left_title, top_title, width_title, height_title)
    # Get the text frame of the title textbox.
    text_frame_title = text_box_title.text_frame
    # Set the title text.
    text_frame_title.text = "Merci pour votre attention"
    # Apply font formatting to all paragraphs and runs within the title text frame.
    for paragraph in text_frame_title.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(30)  # Set font size.
            run.font.bold = True    # Set font to bold.
            run.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.

    # --- Format the Content with Contact and Social Info ---
    # Construct the content text string, pulling data from the 'data' dictionary.
    # Default messages are provided if contact information is missing.
    content_text = (
        f"Contactez-nous\n\n"
        f"Email: {data.get('email', 'ajouter votre email')}\n"
        f"Téléphone: {data.get('phone', 'ajouter votre numero de telephone')}\n"
        f"Site web: {data.get('website_url', '')}\n\n"
        f"Suivez-nous sur:\n"
        f"Facebook: {data.get('facebook_url', '')}\n"
        f"Instagram: {data.get('instagram_url', '')}\n"
        f"Youtube: {data.get('youtube_url', '')}\n"
        f"LinkedIn: {data.get('linkedin_url', '')}"
    )

    # --- Add the Content under the Title ---
    # Define the position and size for the content textbox.
    left_content, top_content, width_content, height_content = Inches(1), Inches(1.5), Inches(6), Inches(3)
    # Add the textbox shape for the content.
    text_box_content = slide.shapes.add_textbox(left_content, top_content, width_content, height_content)
    # Get the text frame of the content textbox.
    text_frame_content = text_box_content.text_frame
    # Set the content text.
    text_frame_content.text = content_text
    # Apply font formatting to all paragraphs and runs within the content text frame.
    for paragraph in text_frame_content.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(15)  # Set font size.
            run.font.bold = False   # Set font to not bold.
            run.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.
