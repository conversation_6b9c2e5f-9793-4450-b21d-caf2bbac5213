"""
Competition slide module.
Handles the creation and formatting of the competition analysis slide.
"""

import json
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from src.gemini_api import generate_content

def create_concurrence_slide(prs, data):
    """
    Creates the competition analysis slide.
    
    Args:
        prs: PowerPoint presentation object
        data: Dictionary containing project data
    """
    # Generate competition analysis using Gemini
    concurrence_data = generate_competition_content(data)
    
    # Access the ninth slide (index 8)
    slide = prs.slides[8]
    
    # Add competition analysis to slide
    them = format_points(concurrence_data.get('them', []))
    us = format_points(concurrence_data.get('us', []))
    
    add_concurrence_section(slide, them, 1, 3.2)
    add_concurrence_section(slide, us, 5, 3.2)

def generate_competition_content(data):
    """
    Generates competition analysis content using Gemini.
    """
    prompt_concurrence = (
        "Compare notre projet avec ceux du même secteur en suivant ce format:\n"
        "IMPORTANT:\n"
        "1. Réponds UNIQUEMENT en JSON valide\n"
        "2. Chaque point DOIT faire maximum 5-6 mots\n"
        "3. Sois direct et précis\n"
        "4. Format exact à suivre:\n"
        "{\n"
        '    "them": ["Premier point clé des concurrents", "Deuxième point clé des concurrents", "Troisième point clé des concurrents"],\n'
        '    "us": ["Premier point clé de notre projet", "Deuxième point clé de notre projet", "Troisième point clé de notre projet"]\n'
        "}\n"
        f"Voici notre projet : {data}"
    )
    
    try:
        response = generate_content(prompt_concurrence)
        # Handle both string and object responses
        response_text = response.text if hasattr(response, 'text') else response
        json_str = clean_json_response(response_text)
        return json.loads(json_str)
    except Exception as e:
        print(f"Error generating competition content: {e}")
        return get_default_competition_content()

def clean_json_response(text):
    """Cleans and extracts JSON from Gemini response."""
    json_start = text.find('{')
    json_end = text.rfind('}') + 1
    if json_start != -1 and json_end != -1:
        return text[json_start:json_end]
    return text

def get_default_competition_content():
    """Returns default competition content if generation fails."""
    return {
        "them": ["Avantage 1 concurrents", "Avantage 2 concurrents", "Avantage 3 concurrents"],
        "us": ["Notre avantage 1", "Notre avantage 2", "Notre avantage 3"]
    }

def format_points(points):
    """Formats points for display."""
    if not points or not isinstance(points, list):
        return "- Donnée non disponible\n- Donnée non disponible\n- Donnée non disponible"
    return '\n'.join(f"- {point}" for point in points[:3])

def add_concurrence_section(slide, content, left, top):
    """
    Adds a competition section to the slide.
    """
    text_box = slide.shapes.add_textbox(Inches(left), Inches(top), Inches(6), Inches(6))
    text_frame = text_box.text_frame
    text_frame.word_wrap = True
    
    p = text_frame.add_paragraph()
    p.text = content
    p.font.size = Pt(14)
    p.font.bold = False
    p.font.color.rgb = RGBColor(0, 0, 0)
    p.alignment = PP_ALIGN.LEFT
