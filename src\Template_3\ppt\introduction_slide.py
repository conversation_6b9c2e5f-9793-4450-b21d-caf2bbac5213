"""
Introduction slide module.
Handles the creation and formatting of the project introduction/about slide.
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from src.gemini_api import generate_content

def create_introduction_slide(prs, data):
    """
    Creates the introduction/about slide with project description.
    
    Args:
        prs: PowerPoint presentation object
        data: Dictionary containing project data
    """
    # Generate about text using Gemini
    prompt_intro = (
        f"Rédige une introduction concise et percutante pour le projet '{data}' "
        "en mettant en avant son objectif principal et son impact. "
        "Fais en sorte que le texte soit bien structuré en 4 phrases courtes, "
        "avec des retours à la ligne pour une meilleure lisibilité."
    )
    
    about_text = generate_content(prompt_intro)
    wrapped_text = wrap_text(about_text, 9)

    # Access the third slide (index 2)
    slide = prs.slides[2]
    
    # Add text box with about text
    left_text = Inches(1.5)
    top_text = Inches(1.5)
    width_text = Inches(6)
    height_text = Inches(3)
    
    text_box = slide.shapes.add_textbox(left_text, top_text, width_text, height_text)
    text_frame = text_box.text_frame
    text_frame.text = wrapped_text
    
    # Format text
    for paragraph in text_frame.paragraphs:
        paragraph.alignment = PP_ALIGN.CENTER
        for run in paragraph.runs:
            run.font.size = Pt(16)
            run.font.bold = False
            run.font.color.rgb = RGBColor(0, 0, 0)

def wrap_text(text, words_per_line=8):
    """
    Wraps text to specified words per line.
    """
    words = text.split()
    lines = [" ".join(words[i:i + words_per_line]) for i in range(0, len(words), words_per_line)]
    return "\n".join(lines)
