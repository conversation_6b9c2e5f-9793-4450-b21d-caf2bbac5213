"""
Funding Request slide module.
Handles the creation and formatting of the funding request slide.
"""

import math
from pptx.util import <PERSON><PERSON>, Pt
from pptx.dml.color import RGBColor
from pptx.chart.data import CategoryChartData
from pptx.enum.chart import XL_CHART_TYPE, XL_LEGEND_POSITION, XL_DATA_LABEL_POSITION

def create_funding_request_slide(prs, data):
    """
    Creates the funding request slide with a chart showing funding sources.
    
    Args:
        prs: PowerPoint presentation object
        data: Dictionary containing project data
        slide_index: Index of the funding slide (default=11)
    """
    # Get slide
    slide = prs.slides[11]
    
    # Extract funding data with validation
    try:
        ressources = {}
        # First, try to get ressources_data from data["volume_vente"][3]
        if "volume_vente" in data and isinstance(data["volume_vente"], list) and len(data["volume_vente"]) > 3:
            potential_ressources = data["volume_vente"][3]
            if isinstance(potential_ressources, dict) and "ressources_data" in potential_ressources:
                ressources = potential_ressources.get("ressources_data", {})
                print("✅ Extraction réussie des données de financement depuis 'volume_vente'.")
            else:
                # If volume_vente[3] exists but doesn't contain ressources_data, fall back to direct data
                ressources = data.get("ressources_data", {})
                print("✅ Extraction réussie des données de financement directement depuis 'data' (volume_vente[3] did not contain ressources_data).")
        else:
            # If volume_vente path is not valid, get directly from data
            ressources = data.get("ressources_data", {})
            print("✅ Extraction réussie des données de financement directement depuis 'data'.")
            
        apport = ressources.get("Apports_des_associes", 0)
        pret_subvention = ressources.get("Prets_et_subvention", 0)
        financement_akkan = ressources.get("Crowdfunding_akkan", 0)
        total_financement = apport + pret_subvention + financement_akkan
    except (KeyError, IndexError, TypeError) as e:
        print(f"❌ ERREUR lors de l'extraction des données de financement: {str(e)}")
        print("⚠️ Utilisation de valeurs par défaut pour le financement.")
        apport = pret_subvention = financement_akkan = total_financement = 0

    if total_financement > 0:
        # Create chart data
        labels = ["Apport des associés", "Prêts et subvention", "Crowdfunding akkan"]
        sizes = [apport, pret_subvention, financement_akkan]

        chart_data = CategoryChartData()
        chart_data.categories = ['']
        
        # Add series with values
        for label, size in zip(labels, sizes):
            chart_data.add_series(label, (size,))

        # Add and configure chart
        chart = slide.shapes.add_chart(
            XL_CHART_TYPE.COLUMN_CLUSTERED, 
            Inches(2.5), Inches(1), Inches(5), Inches(4), chart_data  # Pass chart_data here
        ).chart

        # Configure chart appearance
        chart.has_legend = True
        chart.legend.position = XL_LEGEND_POSITION.BOTTOM
        chart.legend.font.size = Pt(10)
        chart.legend.include_in_layout = False

        # Set custom colors for series
        series_colors = [
            RGBColor(0, 102, 255),    # Blue
            RGBColor(0, 209, 255),    # Light blue
            RGBColor(0, 255, 255)     # Cyan
        ]

        # Format series and data labels
        for i, series in enumerate(chart.series):
            series.format.fill.solid()
            series.format.fill.fore_color.rgb = series_colors[i]
            series.has_data_labels = True
            
            data_labels = series.data_labels
            data_labels.font.size = Pt(12)
            data_labels.font.bold = True
            data_labels.position = XL_DATA_LABEL_POSITION.OUTSIDE_END
            data_labels.number_format = '#,##0" DH"'
            data_labels.font.color.rgb = RGBColor(0, 0, 0)

        # Configure value axis
        value_axis = chart.value_axis
        value_axis.has_major_gridlines = True
        value_axis.major_gridlines.format.line.color.rgb = RGBColor(200, 200, 200)
        
        # Calculate axis scale
        max_value = max(sizes)
        scale_max = math.ceil(max_value / 100000) * 100000
        
        value_axis.maximum_scale = scale_max
        value_axis.minimum_scale = 0
        value_axis.tick_labels.font.size = Pt(10)
        value_axis.number_format = '#,##0.00'
        value_axis.major_unit = scale_max / 7

        # Hide category axis
        category_axis = chart.category_axis
        category_axis.visible = False

        print("✅ Graphique de financement généré avec succès")
    else:
        print("⚠️ Aucune donnée de financement valide à afficher")
