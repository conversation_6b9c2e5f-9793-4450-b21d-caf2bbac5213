from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from src.gemini_api import generate_content
from src.Template_2.text_utils import wrap_text

def add_problem_slide(prs, data):
    """
    Adds the 'problem' slide to the presentation using Template 2 logic.

    This function generates a concise description of the problem that the project
    aims to solve using Google's Gemini AI, focusing on current challenges and
    unmet needs. The generated text is then added to the problem slide in the
    PowerPoint presentation, with specific styling.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, used as input for AI content generation.
    """
    # Access the specific slide intended for the problem statement in Template 2.
    # In this template, it's assumed to be the 4th slide (index 3).
    slide = prs.slides[3]

    # --- Generate Problem Text with Gemini AI ---
    # Define the prompt for Gemini AI to generate a problem description.
    # The prompt specifies focusing on challenges, a clear and direct tone,
    # and a structured output (3-4 explicit sentences, max 10 lines).
    prompt_problem = (
        f"Présente une petite description du problème que le projet '{data}' résout, "
        "en mettant en avant les défis actuels, les obstacles ou les besoins non satisfaits. "
        "Le ton doit être clair, direct et orienté sur les enjeux. "
        "Rédige 3-4 phrases explicites en 10 lignes maximum."
    )

    # Generate content using the Gemini API.
    problem_text_raw = generate_content(prompt_problem)
    # Wrap the generated text to ensure it fits within the slide's layout, with a line limit of 11.
    problem_text = wrap_text(problem_text_raw, 11)

    # --- Add Problem Content to the Slide ---
    # Define the position and size for the text box.
    left_text_problem = Inches(1.5)
    top_text_problem = Inches(2.5)
    width_text_problem = Inches(6)
    height_text_problem = Inches(3)

    # Add a textbox shape to the slide at the specified position and size.
    text_box_problem = slide.shapes.add_textbox(left_text_problem, top_text_problem, width_text_problem, height_text_problem)
    # Get the text frame of the textbox.
    text_frame_problem = text_box_problem.text_frame
    # Set the text content.
    text_frame_problem.text = problem_text

    # Apply font formatting to all paragraphs and runs within the text frame.
    for paragraph in text_frame_problem.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(35)  # Set font size.
            run.font.bold = False   # Set font to not bold.
            run.font.color.rgb = RGBColor(0, 0, 0) # Set font color to black.
