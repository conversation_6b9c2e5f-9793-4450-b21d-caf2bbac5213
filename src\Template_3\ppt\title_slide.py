"""
Title slide module.
Handles the creation and formatting of the title slide.
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor

def create_title_slide(prs, data):
    """
    Creates the title slide with project name.
    
    Args:
        prs: PowerPoint presentation object
        data: Dictionary containing project data
    """
    # Access the first slide
    slide = prs.slides[0]
    
    # Get project name from data
    nom_projet = data.get("name", "Projet Inconnu")
    
    # Add text box with project name
    left_text = Inches(0.5)
    top_text = Inches(3)
    width_text = Inches(3.5)
    height_text = Inches(2)
    
    text_box = slide.shapes.add_textbox(left_text, top_text, width_text, height_text)
    text_frame = text_box.text_frame
    text_frame.text = f"{nom_projet}"
    
    # Format text
    for paragraph in text_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(40)
            run.font.bold = True
            run.font.color.rgb = RGBColor(0, 32, 128)
