"""
Text utilities module.
Contains text formatting helpers such as wrapping, splitting, and other text processing functions.
"""

def wrap_text(text: str, words_per_line: int = 8) -> str:
    """
    Wraps text so that each line contains at most words_per_line words.
    Args:
        text (str): The input text.
        words_per_line (int): Number of words per line.
    Returns:
        str: The wrapped text.
    """
    words = text.split()
    lines = [" ".join(words[i:i + words_per_line]) for i in range(0, len(words), words_per_line)]
    return "\n".join(lines)

def split_text(text: str, max_words_per_line: int = 4) -> str:
    """
    Splits text into lines with a maximum number of words per line.
    Args:
        text (str): The input text.
        max_words_per_line (int): Maximum words per line.
    Returns:
        str: The split text.
    """
    words = text.split()
    lines = []
    current_line = []
    for word in words:
        current_line.append(word)
        if len(current_line) >= max_words_per_line:
            lines.append(" " + " ".join(current_line))
            current_line = []
    if current_line:
        lines.append(" " + " ".join(current_line))
    return "\n".join(lines)
