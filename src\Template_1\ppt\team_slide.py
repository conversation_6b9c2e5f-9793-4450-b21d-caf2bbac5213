"""
Team slide module.
Responsible for rendering the team section of the pitch deck.
"""

from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from pptx.enum.shapes import MSO_SHAPE

def add_team_slide(prs, data):
    """
    Adds the team content to the appropriate slide in the presentation.

    This function dynamically populates the team slide with information about
    each team member, including their position and salary. It arranges team
    members in a circular layout with associated text boxes, applying specific
    styling for visual presentation.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, containing 'equipes' (team members) information.
    """
    # Extract all team positions from the 'equipes' list, filtering out entries without a 'post_name'.
    team_positions = [member for member in data.get("equipes", []) if member.get("post_name")]

    # --- Layout Parameters for Team Member Circles ---
    circle_diameter = Inches(1.5)      # Diameter of each circular shape.
    spacing_horizontal = Inches(0.4)   # Horizontal spacing between circles.
    spacing_vertical = Inches(1.9)     # Vertical spacing between rows of circles.
    max_circles_per_row = 4            # Maximum number of circles to display in a single row.

    num_positions = len(team_positions)
    # Calculate the number of rows needed based on the total number of positions and circles per row.
    num_rows = (num_positions + max_circles_per_row - 1) // max_circles_per_row

    # Access the specific slide intended for the team section.
    # In this template, it's assumed to be the 9th slide (index 8).
    slide = prs.slides[8]

    # --- Iterate and Add Each Team Member's Information ---
    for i, member in enumerate(team_positions):
        position = member["post_name"]      # Get the team member's position.
        salaire = member.get("salaire_brut", "") # Get the team member's salary, defaulting to empty string.

        row = i // max_circles_per_row      # Calculate the row index for the current member.
        col = i % max_circles_per_row       # Calculate the column index for the current member.

        # Calculate the total width occupied by circles in the current row.
        # This ensures proper centering even if the last row has fewer members.
        total_width_row = min(max_circles_per_row, num_positions - row * max_circles_per_row) * circle_diameter + \
                          (min(max_circles_per_row, num_positions - row * max_circles_per_row) - 1) * spacing_horizontal
        # Calculate the starting left position to center the current row of circles.
        start_left_row = (Inches(10) - total_width_row) / 2

        # Calculate the exact position (left, top) for the current circle.
        left = start_left_row + col * (circle_diameter + spacing_horizontal)
        top = Inches(1) + row * spacing_vertical

        # --- Add the Circle Shape ---
        circle = slide.shapes.add_shape(
            MSO_SHAPE.OVAL,         # Shape type: Oval (circle).
            left,
            top,
            circle_diameter,
            circle_diameter
        )

        # --- Style the Circle with a Gradient Fill ---
        fill = circle.fill
        fill.gradient()             # Enable gradient fill.
        fill.gradient_angle = 90.0  # Set gradient angle to 90 degrees.
        # Define gradient stops with specific colors (light blue to purple).
        fill.gradient_stops[0].position = 0
        fill.gradient_stops[0].color.rgb = RGBColor(176, 224, 230) # Powder Blue.
        fill.gradient_stops[1].position = 1
        fill.gradient_stops[1].color.rgb = RGBColor(147, 112, 219) # Medium Purple.

        # --- Add the Position Name Text Box ---
        text_box = slide.shapes.add_textbox(
            left,
            top + circle_diameter/2.5, # Position text slightly below the center of the circle.
            circle_diameter - 0.2,     # Slightly smaller width than the circle.
            Inches(0.5)
        )
        text_frame = text_box.text_frame
        text_frame.word_wrap = True # Enable word wrapping.
        text_frame.text = position  # Set the text to the team member's position.

        # Apply font formatting to the position text.
        for paragraph in text_frame.paragraphs:
            paragraph.alignment = PP_ALIGN.CENTER # Center align the text.
            for run in paragraph.runs:
                run.font.size = Pt(10)  # Set font size.
                run.font.bold = True    # Set font to bold.
                run.font.color.rgb = RGBColor(255, 255, 255) # White text.

        # --- Add the Salary Description Text Box ---
        desc_box = slide.shapes.add_textbox(
            left,
            top + circle_diameter + Inches(0.1), # Position below the circle.
            circle_diameter,
            Inches(0.5)
        )
        desc_frame = desc_box.text_frame
        desc_frame.word_wrap = True # Enable word wrapping.
        desc_frame.text = f"Salaire: {salaire} DH" # Set the text to the formatted salary.

        # Apply font formatting to the salary description.
        for paragraph in desc_frame.paragraphs:
            paragraph.alignment = PP_ALIGN.CENTER # Center align the text.
            for run in paragraph.runs:
                run.font.size = Pt(9)   # Set font size.
                run.font.color.rgb = RGBColor(255, 255, 255) # White text.
