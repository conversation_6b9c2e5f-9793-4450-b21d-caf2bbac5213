from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN

def add_milestones_slide(prs, data):
    """
    Adds the milestones/jalons slide to the presentation using Template 2 logic.
    Only the first milestones slide is handled here.

    This function populates a slide with a table detailing project milestones.
    It retrieves milestone data from the `data` dictionary, formats it, and
    then adds it to a structured table on the slide, applying specific styling
    to headers and content cells.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, containing 'jalons' (milestones) information.
    """
    # Retrieve milestones data from the 'data' dictionary, defaulting to an empty list.
    jalons = data.get("jalons", [])
    rows_per_slide = 6 # Maximum number of milestone rows to display per slide.
    current_slide_index = 12 # The index of the slide intended for milestones in Template 2.
    slide = prs.slides[current_slide_index]

    # Determine the range of milestones to display on this slide (first 6).
    start_index = 0
    end_index = min(start_index + rows_per_slide, len(jalons))
    current_jalons = jalons[start_index:end_index]

    # --- Table Layout Parameters ---
    table_top = Inches(2.5)
    table_left = Inches(1.5)
    table_width = Inches(8.5)
    # Calculate table height based on the number of milestones plus one row for headers.
    table_height = Inches(1) * (len(current_jalons) + 1)

    # Add a table shape to the slide.
    table = slide.shapes.add_table(
        rows=len(current_jalons) + 1, # Number of rows (headers + milestones).
        cols=5,                       # Number of columns.
        left=table_left,
        top=table_top,
        width=table_width,
        height=table_height
    ).table

    # --- Fill Table Headers ---
    headers = ["Jalon", "Date de début", "Période", "Montant (DH)", "Utilisation"]
    for col_index, header in enumerate(headers):
        cell = table.cell(0, col_index) # Access the header cell.
        cell.text = header             # Set header text.
        cell.fill.solid()              # Set solid fill.
        cell.fill.fore_color.rgb = RGBColor(0, 128, 128) # Set fill color to Teal.
        # Apply font formatting to header text.
        for paragraph in cell.text_frame.paragraphs:
            paragraph.alignment = PP_ALIGN.CENTER # Center align text.
            for run in paragraph.runs:
                run.font.size = Pt(20)  # Set font size.
                run.font.bold = True    # Set font to bold.
                run.font.color.rgb = RGBColor(255, 255, 255) # White text.

    # --- Populate Table Rows with Milestone Data ---
    for row_index, jalon in enumerate(current_jalons, start=1):
        # Format 'periode' (term) or set default.
        periode = f"{jalon['term']} Jours" if jalon['term'] else "T0 (lancement)"
        num_jalon = row_index # Milestone number.
        # Use 'name' or 'description' for milestone name.
        nom_etape = jalon['name'] if jalon['name'] else jalon['description']
        # Format 'amount' or set default.
        montant = f"{jalon['amount']:,.2f} DH" if jalon['amount'] else "Montant non spécifié"
        # Use 'description' for 'utilisation' or set default.
        utilisation = jalon['description'] if jalon['description'] else "Non spécifié"
        
        # Format 'date_delivrable'.
        date_debut_str = jalon.get('date_delivrable', '')
        if date_debut_str:
            date_debut_formatted = date_debut_str.split('T')[0] # Extract date part from ISO format.
        else:
            date_debut_formatted = 'Non spécifiée' # Default if date is missing.

        # Set text for each cell in the current row.
        table.cell(row_index, 0).text = str(num_jalon)
        table.cell(row_index, 1).text = date_debut_formatted
        table.cell(row_index, 2).text = periode
        table.cell(row_index, 3).text = montant
        table.cell(row_index, 4).text = utilisation

        # Style each cell in the current row.
        for col_index in range(5):
            cell = table.cell(row_index, col_index)
            cell.fill.solid() # Set solid fill.
            cell.fill.fore_color.rgb = RGBColor(240, 240, 240) # Light gray fill.
            # Apply font formatting to cell text.
            for paragraph in cell.text_frame.paragraphs:
                paragraph.alignment = PP_ALIGN.LEFT # Left align text.
                for run in paragraph.runs:
                    run.font.size = Pt(20)  # Set font size.
                    run.font.color.rgb = RGBColor(0, 0, 0) # Black text.

    # --- Set Column Widths ---
    column_widths = [Inches(0.8), Inches(2), Inches(3.0), Inches(2.5), Inches(7.0)]
    for col_index, width in enumerate(column_widths):
        table.columns[col_index].width = width # Set specific width for each column.
