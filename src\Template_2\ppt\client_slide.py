from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.enum.shapes import MSO_SHAPE
from src.gemini_api import generate_content

def add_client_slide(prs, data):
    """
    Adds the client slides to the presentation using Template 2 logic.
    Handles client distribution and recommended segments.

    This function populates a slide with information about target clients,
    distributing them into columns. It also generates and displays
    recommended client segments using Google's Gemini AI, styled as
    rounded rectangles at the bottom of the slide.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, containing 'clients'
                     (target clients) and other information for AI content generation.
    """
    # Access the specific slide intended for client information in Template 2.
    # In this template, it's assumed to be the 7th slide (index 6).
    slide7 = prs.slides[6]
    # Retrieve target clients data from the 'data' dictionary, defaulting to an empty list.
    clients_cibles = data.get("clients", [])

    # --- Client Distribution Logic for Columns ---
    total_clients = len(clients_cibles)
    max_per_col_small = 3  # Max clients per column for smaller sets.
    max_per_col_large = 6  # Max clients per column for larger sets.
    max_on_slide_small = max_per_col_small * 2 # Max clients on slide if using small columns.
    max_on_slide_large = max_per_col_large * 2 # Max clients on slide if using large columns.

    # Determine how many clients go into each column based on total count.
    if total_clients <= max_on_slide_small:
        clients_in_left_col_s7 = min(total_clients, max_per_col_small)
        clients_in_right_col_s7 = total_clients - clients_in_left_col_s7
    else:
        clients_in_left_col_s7 = min(total_clients, max_per_col_large)
        clients_in_right_col_s7 = min(total_clients - clients_in_left_col_s7, max_per_col_large)

    # Determine the number of columns to display.
    num_cols_on_s7 = 1 if clients_in_right_col_s7 == 0 else 2
    clients_on_s7 = clients_in_left_col_s7 + clients_in_right_col_s7

    # --- Layout Parameters for Client Columns ---
    total_slide_width_inches = prs.slide_width.inches
    margin_inches = 0.75
    gap_between_cols_inches = 0.5
    top_pos = Inches(2.8)
    col_height = Inches(5.0)

    # Calculate usable width and individual column width.
    usable_width_s7 = total_slide_width_inches - (2 * margin_inches)
    col_width_inches_s7 = (usable_width_s7 - (num_cols_on_s7 - 1) * gap_between_cols_inches) / num_cols_on_s7 if num_cols_on_s7 > 0 else usable_width_s7

    # Calculate left positions for columns.
    left_col1_s7 = Inches(margin_inches)
    left_col2_s7 = Inches(margin_inches + col_width_inches_s7 + gap_between_cols_inches)

    # --- Populate Client Columns ---
    for col_index in range(num_cols_on_s7):
        left = left_col1_s7 if col_index == 0 else left_col2_s7
        # Add a textbox for the current column.
        text_box = slide7.shapes.add_textbox(left, top_pos, Inches(col_width_inches_s7), col_height)
        text_frame = text_box.text_frame
        text_frame.word_wrap = True
        text_frame.margin_bottom = Inches(0.1)
        text_frame.margin_top = Inches(0.1)

        # Determine the range of clients for the current column.
        if col_index == 0:
            start_index = 0
            end_index = clients_in_left_col_s7
        else:
            start_index = clients_in_left_col_s7
            end_index = clients_on_s7

        # Add each client's name with a numbered bullet.
        for i, client in enumerate(clients_cibles[start_index:end_index]):
            client_name = client.get("name", "Nom non spécifié")
            p = text_frame.add_paragraph() # Add a new paragraph for each client.
            p = text_frame.add_paragraph() # Add another paragraph for spacing/layout.
            p.alignment = PP_ALIGN.LEFT
            p.level = 0 # Set paragraph level (for bulleting).

            # Add the numbered run.
            num_run = p.add_run()
            num_run.text = f"{start_index + i + 1:02}. " # Format number with leading zero.
            num_run.font.size = Pt(35)
            num_run.font.bold = True
            num_run.font.color.rgb = RGBColor(47, 79, 79) # Dark Slate Gray.

            # Add the client name run.
            name_run = p.add_run()
            name_run.text = client_name
            name_run.font.size = Pt(30)
            name_run.font.bold = True
            # Apply different text color based on the column.
            if col_index == 0:
                name_run.font.color.rgb = RGBColor(255, 255, 255) # White for left column.
            else:
                name_run.font.color.rgb = RGBColor(0, 0, 0) # Black for right column.

    # --- Generate Recommended Segments with Gemini AI ---
    # Define the prompt for Gemini AI to suggest additional client segments.
    # The prompt asks for creative suggestions not already mentioned, in a brief point format.
    prompt_clients_recommandes = (
        f"En tant qu'expert, en utilisant les informations suivantes sur les clients cibles existants : {data}, "
        "propose d'autres  clients que le porteur du projet pourrait cibler. Ces segments ne doivent pas inclure les clients déjà mentionnés. "
        "Sois créatif et cible des groupes qui pourraient être intéressés par ce projet, mais qui n'ont pas encore été considérés. "
        "Donne ta réponse sous forme de point bref sans explication, en mettant en avant 3 segments de clients . "
        "Format exact à suivre:\nPremier client ,\nDeuxième client,\nTroisième client"
    )
    # Generate content and strip whitespace.
    clients_recommandes_text = generate_content(prompt_clients_recommandes).strip()
    # Split the generated text by comma to get individual segments and strip any extra whitespace.
    segments = [seg.strip() for seg in clients_recommandes_text.split(",") if seg.strip()]
    # Ensure only a maximum of 3 segments are used.
    segments = segments[:3]

    # --- Recommended Segments Styling (Bottom of Slide) ---
    # Define parameters for positioning and sizing the segment boxes.
    box_count = 3
    box_width = Inches(3.2)
    box_height = Inches(1.1)
    gap = Inches(0.4)
    # Calculate the total width occupied by all boxes and gaps.
    total_width = box_count * box_width + (box_count - 1) * gap
    # Calculate the starting left position to center the boxes horizontally.
    left_start = (prs.slide_width - total_width) / 2
    # Calculate the top position for the boxes, closer to the bottom of the slide.
    top_pos_box = prs.slide_height - box_height - Inches(0.5)

    # --- Title above Recommended Segment Boxes ---
    title_text = "AKKAN recommande aussi :"
    title_height = Inches(0.5)
    # Add a textbox for the title above the recommended segments.
    title_box = slide7.shapes.add_textbox(left_start, top_pos_box - title_height - Inches(0.15), total_width, title_height)
    title_frame = title_box.text_frame
    title_frame.clear() # Clear any default text.
    p_title = title_frame.paragraphs[0]
    p_title.text = title_text
    p_title.alignment = PP_ALIGN.CENTER # Center align the title.
    # Apply font formatting to the title.
    for run in p_title.runs:
        run.font.size = Pt(22)  # Set font size.
        run.font.bold = True    # Set font to bold.
        run.font.color.rgb = RGBColor(0, 176, 80) # Green color.

    # --- Segment Boxes (3 horizontally, styled) ---
    # Iterate through the segments to create and style each box.
    for i, seg in enumerate(segments):
        # Calculate the left position for the current box.
        left = left_start + i * (box_width + gap)
        # Add a rounded rectangle shape for the segment box.
        shape = slide7.shapes.add_shape(
            MSO_SHAPE.ROUNDED_RECTANGLE,
            left,
            top_pos_box,
            box_width,
            box_height
        )
        # Apply styling to the shape: light green background, green border, slight shadow.
        shape.fill.solid()
        shape.fill.fore_color.rgb = RGBColor(232, 255, 239) # Light Green Background.
        shape.line.color.rgb = RGBColor(0, 176, 80) # Green Border.
        shape.line.width = Pt(2)
        shape.shadow.inherit = False # Disable shadow inheritance.
        shape.shadow.blur_radius = Pt(6)
        shape.shadow.distance = Pt(1.5)

        # Configure text frame for centered text, green color, not bold, and proper margins.
        text_frame = shape.text_frame
        text_frame.clear() # Clear any default text.
        text_frame.word_wrap = True
        text_frame.vertical_anchor = MSO_ANCHOR.MIDDLE # Vertically center the text.
        text_frame.margin_left = Inches(0.2)
        text_frame.margin_right = Inches(0.2)
        text_frame.margin_top = Inches(0.08)
        text_frame.margin_bottom = Inches(0.08)

        # Add the segment text to the paragraph and apply font formatting.
        p = text_frame.paragraphs[0]
        p.text = seg
        p.alignment = PP_ALIGN.CENTER # Center align the text.
        for run in p.runs:
            run.font.size = Pt(15)  # Set font size.
            run.font.bold = False   # Set font to not bold.
            run.font.color.rgb = RGBColor(34, 102, 60) # Dark Green text.
