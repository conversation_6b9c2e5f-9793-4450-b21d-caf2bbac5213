from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from src.gemini_api import generate_content
from src.Template_2.text_utils import wrap_text

def add_funding_request_slide(prs, data):
    """
    Adds the funding request slide to the presentation using Template 2 logic.

    This function generates a concise and impactful funding request message
    using Google's Gemini AI, explaining the requested amount, type of funding,
    and its purpose. The generated text is then added to the funding request
    slide in the PowerPoint presentation, with specific styling.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, used as input for AI content generation.
    """
    # Access the specific slide intended for the funding request in Template 2.
    # In this template, it's assumed to be the 14th slide (index 13).
    slide = prs.slides[13]

    # --- Generate Funding Request Text with Gemini AI ---
    # Define the prompt for Gemini AI to generate a funding request message.
    # The prompt specifies details to include (amount, type, purpose, cruciality)
    # and desired tone (professional, engaging, motivating).
    prompt_demande = (
        f"À partir de ces données : {data}, rédige une brève section  en expliquant clairement le montant demandé à Akkan "
        "(en MAD),précise le type de financement d'akkan(UN DON , pret ou bien contre valeur)   et à quoi il servira, et pourquoi ce soutien est crucial pour le projet. "
        "Le ton doit rester professionnel, engageant et motivant, sans être trop promotionnel. "
        "Utilise 3 à 4 phrases claires et impactantes en 10 lignes max."
    )

    # Generate content using the Gemini API.
    demande_text_raw = generate_content(prompt_demande)
    # Wrap the generated text to ensure it fits within the slide's layout, with a line limit of 12.
    demande_text = wrap_text(demande_text_raw, 12)

    # --- Add Funding Request Content to the Slide ---
    # Define the position and size for the text box.
    left_text = Inches(1.5)
    top_text = Inches(2.5)
    width_text = Inches(10)
    height_text = Inches(8)

    # Add a textbox shape to the slide at the specified position and size.
    text_box = slide.shapes.add_textbox(left_text, top_text, width_text, height_text)
    # Get the text frame of the textbox.
    text_frame = text_box.text_frame
    # Set the text content.
    text_frame.text = demande_text

    # Apply font formatting to all paragraphs and runs within the text frame.
    for paragraph in text_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(30)  # Set font size.
            run.font.bold = False   # Set font to not bold.
            run.font.color.rgb = RGBColor(0, 0, 0) # Set font color to black.
