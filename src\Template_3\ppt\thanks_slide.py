"""
Thanks slide module.
Handles the creation and formatting of the thank you/contact slide.
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor

def create_thanks_slide(prs, data):
    """
    Creates the thank you/contact slide.
    
    Args:
        prs: PowerPoint presentation object
        data: Dictionary containing project data
    """
    # Access the fourteenth slide (index 13)
    slide = prs.slides[13]
    


    # Add contact information
    left_content = Inches(0.5)
    top_content = Inches(1.5)
    width_content = Inches(6)
    height_content = Inches(3)
    
    content_text = f"\n\n\nEmail: {data.get('email', 'ajouter votre email')}\n" \
                   f"Téléphone: {data.get('phone', 'ajouter votre numero de telephone')}\n" \
                   f"Site web: {data.get('website_url', '')}"
    
    text_box = slide.shapes.add_textbox(left_content, top_content, width_content, height_content)
    text_frame = text_box.text_frame
    text_frame.text = content_text
    
    for paragraph in text_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(15)
            run.font.bold = False
            run.font.color.rgb = RGBColor(0, 0, 0)
