"""
Team slide module.
Handles the creation and formatting of the team slide.
"""

import math
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE

def create_team_slide(prs, data):
    """
    Creates the team slide.
    
    Args:
        prs: PowerPoint presentation object
        data: Dictionary containing project data
    """
    # Access the eleventh slide (index 10)
    slide = prs.slides[10]
    
    # Get team members from data
    team_members = []
    for member in data['equipes']:
        if member['post_name']:
            post_name = ' '.join(word.capitalize() for word in member['post_name'].split())
            team_members.append({
                "name": post_name,
                "color": "light_blue" if len(team_members) % 2 == 0 else "blue"
            })

    # Calculate layout parameters
    num_members = len(team_members)
    cols = min(2, num_members)
    rows = math.ceil(num_members / cols)
    
    # Calculate dimensions and spacing
    slide_width = Inches(10)
    slide_height = Inches(7.5)
    margin_x = Inches(0.5)
    margin_y = Inches(1.2)
    
    usable_width = slide_width - (2 * margin_x)
    usable_height = slide_height - (2 * margin_y)
    
    photo_size = min(
        usable_width / (cols * 1.2),
        usable_height / (rows * 2),
        Inches(0.6)
    )
    photo_size = Inches(float(photo_size.inches))
    
    spacing_x = (usable_width - (cols * photo_size)) / (cols + 1)
    spacing_y = (usable_height - (rows * (photo_size + Inches(0.2)))) / (rows + 1)
    font_size = min(8, int(photo_size.inches * 14))

    # Colors dictionary
    colors = {
        "blue": RGBColor(0, 102, 255),
        "light_blue": RGBColor(0, 209, 255)
    }

    # Position each team member
    for i, member in enumerate(team_members):
        row = i // cols
        col = i % cols
        
        left = margin_x + spacing_x + (col * (photo_size + spacing_x))
        top = margin_y + spacing_y + (row * (photo_size + spacing_y + Inches(0.2)))

        # Add circle (photo placeholder)
        circle = slide.shapes.add_shape(
            MSO_SHAPE.OVAL,
            left,
            top,
            photo_size,
            photo_size
        )
        circle.fill.solid()
        circle.fill.fore_color.rgb = colors[member["color"]]
        circle.line.fill.background()
        
        # Add name
        textbox = slide.shapes.add_textbox(
            left - Inches(0.1),
            top + photo_size,
            photo_size + Inches(0.2),
            Inches(0.2)
        )
        text_frame = textbox.text_frame
        p = text_frame.paragraphs[0]
        p.text = member["name"]
        p.font.size = Pt(font_size)
        p.font.bold = True
        p.alignment = 1  # Center alignment
