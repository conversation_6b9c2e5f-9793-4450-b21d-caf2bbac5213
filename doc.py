import json
import re
from docxtpl import DocxTemplate, RichText, InlineImage
import google.generativeai as genai
from docx.oxml import OxmlElement, parse_xml
from docx.oxml.ns import qn, nsdecls
from docx.enum.table import WD_ALIGN_VERTICAL
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import Pt, Inches, Cm, Mm
import matplotlib.pyplot as plt
import numpy as np
from collections import defaultdict
from io import BytesIO
from src.config import GENAI_API_KEY # Import API key from config

def generate_document_from_data(data: dict, output_path: str, doc_template_path: str):
    """
    Generates a Word document based on the provided data and saves it to the specified path.

    Args:
        data (dict): The JSON data to populate the document.
        output_path (str): The path where the generated .docx file will be saved.
        doc_template_path (str): The path to the Word document template.
    """

    # Configure Gemini API
    genai.configure(api_key=GENAI_API_KEY)
    model = genai.GenerativeModel("gemini-2.0-flash")

    # Use the passed data directly
    json_data = data

    # 2. Extraire les champs requis
    adresse = json_data.get("address_info", "Adresse inconnue")
    telephone = json_data.get("phone", "Téléphone inconnu")
    site_web = json_data.get("website_url", "www.exemple.com")

    # 3. Texte page 1
    texte_page1 = f"{adresse} | {telephone} | {site_web}"

    # 4. Créer des objets RichText avec couleur
    nom_entreprise = RichText(json_data.get("name", "Nom inconnu"), color="000001")
    secteur_activite = RichText(json_data.get("id_domain", "Secteur inconnu"), color="000001")
    adresse_info = RichText(json_data.get("address_info", "Adresse inconnue"), color="000001")
    ville = RichText(json_data.get("city", "Ville inconnue"), color="000001")
    pays = RichText(json_data.get("id_country", "Pays inconnu"), color="000001")

    # 5. Construire le texte page 2
    texte_page2 = RichText()
    texte_page2.add("Nom de l’Entreprise:\n")
    texte_page2.add(nom_entreprise)
    texte_page2.add("\nSecteur d'Activité:\n", color="2d4d31")
    texte_page2.add(secteur_activite)
    texte_page2.add("\nAdresse et Informations de Localisation:\n", color="2d4d31")
    texte_page2.add(adresse_info)
    texte_page2.add("\nVille et pays:\n", color="2d4d31")
    texte_page2.add(ville)
    texte_page2.add(", ")
    texte_page2.add(pays)

    # 6. Générer le Business Plan avec Gemini
    prompt_bp = f"""
    Voici les informations de l' entreprise : {json_data}

    Génère les sections d’un business plan en respectant ce format JSON.
    Sois professionnel et synthétique (3-4 phrases max par section).

    Retourne UNIQUEMENT un JSON strict comme ci-dessous :

    {{
        "concept": "",
        "mission": "",
        "objectifs": "",
        "proposition_valeur": "",
        "marche_cible_et_concurrence": "",
        "besoins_financement": ""
    }}
    """

    # Génération Gemini
    try:
        response_bp = model.generate_content(prompt_bp)
        json_str_bp = response_bp.text.strip()

        if json_str_bp.startswith("```json"):
            json_str_bp = json_str_bp.split("```json")[1]
        if json_str_bp.endswith("```"):
            json_str_bp = json_str_bp.split("```")[0]

        json_str_bp = json_str_bp.strip()
        bp_data = json.loads(json_str_bp)
        print("✅ Contenu du Business Plan généré avec succès !")
    except Exception as e:
        print(f"❌ Erreur : {str(e)}")
        bp_data = {
            "concept": "Concept par défaut.",
            "mission": "Mission par défaut.",
            "objectifs": "Objectifs par défaut.",
            "proposition_valeur": "Valeur unique par défaut.",
            "marche_cible_et_concurrence": "Marché cible par défaut.",
            "besoins_financement": "Financement par défaut."
        }

    # 7. Générer la liste d'offres
    prompt_offres = f"""
    Voici les informations d'une entreprise : {json_data}

    Génère une liste d'offres commerciales proposées par cette entreprise.
    Retourne un JSON strict, de la forme suivante (et rien d’autre autour) :

    [
      {{
        "offre": "Nom de l'offre",
        "type": "Type (ex: Service, Produit, Abonnement)",
        "description": "Courte description de 6-10 mots"
      }},
      ...
    ]
    """

    response_offres = model.generate_content(prompt_offres)
    json_str_offres = response_offres.text.strip()

    if json_str_offres.startswith("```json"):
        json_str_offres = json_str_offres.split("```json")[1].split("```")[0].strip()

    try:
        offres_data = json.loads(json_str_offres)
    except json.JSONDecodeError:
        print("❌ Erreur dans le JSON généré par Gemini")
        offres_data = []

    # 8. Construction du contexte pour docx
    context = {
        "texte_page1": texte_page1,
        "texte_page2": texte_page2,
        "texte1_page3": RichText(bp_data["concept"]),
        "texte2_page3": RichText(bp_data["mission"]),
        "texte3_page3": RichText(bp_data["objectifs"]),
        "texte4_page3": RichText(bp_data["proposition_valeur"]),
        "texte5_page3": RichText(bp_data["marche_cible_et_concurrence"]),
        "texte6_page3": RichText(bp_data["besoins_financement"]),
        "offres": offres_data
    }

    #offre
    data_offers = json_data['offers']
    # Créer un sous-document
    doc = DocxTemplate(doc_template_path)
    subdoc = doc.new_subdoc()

    subdoc.add_paragraph('Liste des Offres').style.font.size = Pt(14)

    # Créer le tableau
    table = subdoc.add_table(rows=len(data_offers) + 1, cols=3)
    table.style = "Table Grid"

    # Définir les largeurs de colonnes
    column_widths = [Inches(2), Inches(0.5), Inches(4.5)]  # Offre, Type, Description


    def set_cell_background(cell, color):
        """Définit un arrière-plan de couleur HEX à une cellule Word."""
        cell._tc.get_or_add_tcPr().append(parse_xml(
            r'<w:shd {} w:fill="{}"/>'.format(nsdecls('w'), color)
        ))


    # # En-têtes
    hdr_cells = table.rows[0].cells
    headers = ['Offre', 'Type', 'Description']
    for i, header in enumerate(headers):
        hdr_cells[i].text = header
        hdr_cells[i].width = column_widths[i]
        hdr_cells = table.rows[0].cells

        set_cell_background(hdr_cells[i], "04A264")  # fond vert

    # # Remplir les lignes
    for i, item in enumerate(data_offers, 1):
        row_cells = table.rows[i].cells
        row_cells[0].text = item["name"]
        row_cells[1].text = item["id_project_type_offer"]
        row_cells[2].text = item["description"]

        # Appliquer les largeurs à chaque cellule
        for j in range(3):
            row_cells[j].width = column_widths[j]

    #Centrer le tableau dans la page
    tbl = table._tbl
    tblPr = tbl.tblPr
    if tblPr is None:
        tblPr = OxmlElement('w:tblPr')
        tbl.insert(0, tblPr)

    jc = OxmlElement('w:jc')
    jc.set(qn('w:val'), 'center')
    tblPr.append(jc)

    #KPI

    # Créer un deuxième sous-document pour les indicateurs
    subdoc2 = doc.new_subdoc()
    data_kpis = json_data['kpis']
    # Créer le tableau
    table2 = subdoc2.add_table(rows=len(data_kpis) + 1, cols=3)
    table2.style = "Table Grid"

    # Définir les largeurs de colonnes pour ce tableau
    column_widths2 = [Inches(2.5), Inches(3.0), Inches(1)]  # Indicateur, Description, Valeur

    #En-têtes
    hdr_cells2 = table2.rows[0].cells
    headers2 = ['Indicateur', 'Description', 'Valeur']
    for i, header in enumerate(headers2):
        hdr_cells2[i].text = header
        hdr_cells2[i].width = column_widths2[i]
        set_cell_background(hdr_cells2[i], "04A264")  # fond vert

    # # Remplir les lignes
    for i, item in enumerate(data_kpis, 1):
        row_cells = table2.rows[i].cells
        row_cells[0].text = item["name"]
        row_cells[1].text = item["description"]
        row_cells[2].text = str(item["number"])

        # Appliquer les largeurs à chaque cellule
        for j in range(3):
            row_cells[j].width = column_widths2[j]

    # # Centrer le tableau dans la page
    tbl2 = table2._tbl
    tblPr2 = tbl2.tblPr
    if tblPr2 is None:
        tblPr2 = OxmlElement('w:tblPr')
        tbl2.insert(0, tblPr2)

    jc2 = OxmlElement('w:jc')
    jc2.set(qn('w:val'), 'center')
    tblPr2.append(jc2)


    # ODD

    # # Créer un troisième sous-document pour les objectifs
    subdoc3 = doc.new_subdoc()
    data_objectifs = json_data['objectifs']
    # Créer le tableau
    table3 = subdoc3.add_table(rows=len(data_objectifs) + 1, cols=3)
    table3.style = "Table Grid"

    # # Définir les largeurs de colonnes pour ce tableau
    column_widths3 = [Inches(2), Inches(3.0), Inches(1.5)]  # ODD, Définition, Implementation

    # # En-têtes
    hdr_cells3 = table3.rows[0].cells
    headers3 = ['Objectifs de Développement Durables', 'Définition de l’impact KPI', 'Mettre en œuvre cet indicateur']
    for i, header in enumerate(headers3):
        hdr_cells3[i].text = header
        hdr_cells3[i].width = column_widths3[i]
        set_cell_background(hdr_cells3[i], "04A264")  # fond vert

    # # Remplir les lignes
    for i, item in enumerate(data_objectifs, 1):
        row_cells = table3.rows[i].cells
        row_cells[0].text = item["name"]
        row_cells[1].text = item["description"]
        row_cells[2].text = item["id_project_type_objective"]

    #     # Appliquer les largeurs à chaque cellule
        for j in range(3):
            row_cells[j].width = column_widths3[j]

    # # Centrer le tableau dans la page
    tbl3 = table3._tbl
    tblPr3 = tbl3.tblPr
    if tblPr3 is None:
        tblPr3 = OxmlElement('w:tblPr')
        tbl3.insert(0, tblPr3)

    jc3 = OxmlElement('w:jc')
    jc3.set(qn('w:val'), 'center')
    tblPr3.append(jc3)

    # Contexte
    context_tables = {
        "texte1_page4": subdoc,
        "texte1_page5": subdoc2,
        "texte1_page6": subdoc3
    }

    # Merge contexts
    context.update(context_tables)

    # Partenaires
    subdoc4 = doc.new_subdoc()
    data_partners = json_data['partners']

    # # Créer le tableau
    table4 = subdoc4.add_table(rows=len(data_partners) + 1, cols=3)
    table4.style = "Table Grid"

    # # Définir les largeurs de colonnes pour ce tableau
    column_widths4 = [Inches(2), Inches(2), Inches(2)]  # Partenaire, Type, Niveau d'engagement

    # # En-têtes
    hdr_cells4 = table4.rows[0].cells
    headers4 = ['Partenaire', 'Type', 'Niveau d\'engagement']
    for i, header in enumerate(headers4):
        hdr_cells4[i].text = header
        hdr_cells4[i].width = column_widths4[i]
        set_cell_background(hdr_cells4[i], "04A264")  # fond vert

    # # Remplir les lignes
    for i, item in enumerate(data_partners, 1):
        row_cells = table4.rows[i].cells
        row_cells[0].text = item["name"]
        row_cells[1].text = item["nature"]
        row_cells[2].text = item["id_partner_avancement"]

    #     # Appliquer les largeurs à chaque cellule
        for j in range(3):
            row_cells[j].width = column_widths4[j]

    # # Centrer le tableau dans la page
    tbl4 = table4._tbl
    tblPr4 = tbl4.tblPr
    if tblPr4 is None:
        tblPr4 = OxmlElement('w:tblPr')
        tbl4.insert(0, tblPr4)

    jc4 = OxmlElement('w:jc')
    jc4.set(qn('w:val'), 'center')
    tblPr4.append(jc4)

    context_tables["texte1_page7"] = subdoc4
    context.update(context_tables)

    # Fournisseurs
    subdoc5 = doc.new_subdoc()

    # Check if 'fournisseurs' key exists in the JSON data
    if 'fournisseurs' in json_data:
        data_fournisseurs = json_data['fournisseurs']
    else:
        # Create a default empty list if the key doesn't exist
        data_fournisseurs = []

    # Create the table only if there are fournisseurs
    if data_fournisseurs:
        # Créer le tableau
        table5 = subdoc5.add_table(rows=len(data_fournisseurs) + 1, cols=2)
        table5.style = "Table Grid"

        # Définir les largeurs de colonnes pour ce tableau
        column_widths5 = [Inches(2), Inches(4)]  # Fournisseur, Description

        # En-têtes
        hdr_cells5 = table5.rows[0].cells
        headers5 = ['Fournisseur', 'Description']
        for i, header in enumerate(headers5):
            hdr_cells5[i].text = header
            hdr_cells5[i].width = column_widths5[i]
            set_cell_background(hdr_cells5[i], "04A264")  # fond vert

        # Remplir les lignes
        for i, item in enumerate(data_fournisseurs, 1):
            row_cells = table5.rows[i].cells
            # Use get() to avoid KeyError if keys don't exist
            row_cells[0].text = item.get("name", "")
            row_cells[1].text = item.get("description", "")

            # Appliquer les largeurs à chaque cellule
            for j in range(2):
                row_cells[j].width = column_widths5[j]

        # Centrer le tableau dans la page
        tbl5 = table5._tbl
        tblPr5 = tbl5.tblPr
        if tblPr5 is None:
            tblPr5 = OxmlElement('w:tblPr')
            tbl5.insert(0, tblPr5)

        jc5 = OxmlElement('w:jc')
        jc5.set(qn('w:val'), 'center')
        tblPr5.append(jc5)
    else:
        # If no fournisseurs, just add a message
        subdoc5.add_paragraph("Aucun fournisseur n'est disponible pour le moment.")

    # Add to context regardless
    context_tables["texte1_page8"] = subdoc5
    context.update(context_tables)



    # Moyens de communications sous forme de tableau centré
    subdoc6 = doc.new_subdoc()
    data_moyens_com = json_data['moyensComunications']

    # # Créer le tableau
    table6 = subdoc6.add_table(rows=len(data_moyens_com) + 1, cols=2)
    table6.style = "Table Grid"

    # # Largeurs de colonnes
    column_widths6 = [Inches(2), Inches(5.0)]

    # # En-têtes
    hdr_cells6 = table6.rows[0].cells
    headers6 = ["Canal de communication", "Description"]
    for i, header in enumerate(headers6):
        hdr_cells6[i].text = header
        set_cell_background(hdr_cells6[i], "04A264")
        hdr_cells6[i].width = column_widths6[i]
        for paragraph in hdr_cells6[i].paragraphs:
            paragraph.alignment = 1  # Centrer le texte

    # # Contenu du tableau
    for i, item in enumerate(data_moyens_com, 1):
        row_cells = table6.rows[i].cells
        row_cells[0].text = item["name"]
        row_cells[1].text = item["description"]
        row_cells[0].width = column_widths6[0]
        row_cells[1].width = column_widths6[1]
        for cell in row_cells:
            for paragraph in cell.paragraphs:
                paragraph.alignment = 1  # Centrer le texte

    # # Centrer le tableau sur la page
    tbl6 = table6._tbl
    tblPr6 = tbl6.tblPr
    if tblPr6 is None:
        tblPr6 = OxmlElement('w:tblPr')
        tbl6.insert(0, tblPr6)

    jc6 = OxmlElement('w:jc')
    jc6.set(qn('w:val'), 'center')
    tblPr6.append(jc6)

    # # Ajouter au contexte
    context_tables["texte1_page9"] = subdoc6
    context.update(context_tables)


    #suggestion de fidelisation
    # # Extraire les données nécessaires depuis le JSON
    canaux_ventes = [item["name"] for item in json_data.get("canaux_ventes", [])]
    canaux_com = [item["name"] for item in json_data.get("moyensComunications", [])]

    # # Prompt intelligent
    prompt = f"""
    Voici des canaux de vente : {', '.join(canaux_ventes)}
    Voici des canaux de communication utilisés : {', '.join(canaux_com)}

    Donne-moi une liste de suggestions concrètes pour fidéliser les clients en analysant et optimisant ces canaux.
    Présente chaque suggestion sous forme structurée avec deux colonnes :
    1. Le canal concerné (vente ou communication)
    2. Une suggestion de fidélisation claire et pratique
    Formate ta réponse en JSON comme ceci :
    [
      {{"canal": "Site web", "suggestion": "Mettre en place un système de points de fidélité"}},
      ...
    ]
    """

    # # Requête à Gemini
    response = model.generate_content(prompt)
    gemini_text = response.text

    def extract_suggestions(gemini_text):
        """
        Extracts suggestions from Gemini's text response.

        Args:
            gemini_text (str): The text response from Gemini.

        Returns:
            list: A list of suggestions extracted from the text.

        Raises:
            ValueError: If the Gemini response is not in the expected JSON format.
        """
        json_str = re.search(r"\[.*\]", gemini_text, re.DOTALL)
        if json_str:
            json_string = json_str.group(0)
            # Attempt to load the JSON string, handling potential errors
            try:
                suggestions = json.loads(json_string)
                return suggestions
            except json.JSONDecodeError as e:
                print(f"Erreur de décodage JSON: {e}")
                raise ValueError("La réponse de Gemini contient un JSON invalide.") from e
        else:
            raise ValueError("La réponse de Gemini n'est pas au format JSON attendu.")

    def filter_suggestions(suggestions, max_suggestions=8):
        """
        Filters suggestions to keep one per channel and limits the total number of suggestions.

        Args:
            suggestions (list): A list of suggestions, each containing a "canal" and "suggestion".
            max_suggestions (int): The maximum number of suggestions to keep.

        Returns:
            list: The filtered list of suggestions.
        """
        seen_channels = set()
        filtered_suggestions = []

        for suggestion in suggestions:
            canal = suggestion.get("canal")
            if canal not in seen_channels and len(filtered_suggestions) < max_suggestions:
                filtered_suggestions.append(suggestion)
                seen_channels.add(canal)

        return filtered_suggestions

    # # Récupérer les suggestions de Gemini
    suggestions = extract_suggestions(gemini_text)

    # # Appliquer le filtre pour ne garder qu'une suggestion par canal et limiter à 6 suggestions
    filtered_suggestions = filter_suggestions(suggestions, max_suggestions=6)

    # Insertion dans le Word
    subdoc8 = doc.new_subdoc()
    table8 = subdoc8.add_table(rows=len(filtered_suggestions) + 1, cols=2)
    table8.style = "Table Grid"
    column_widths8 = [Inches(2.5), Inches(4.5)]

    # # En-têtes
    headers8 = ["Canal", "Suggestion de fidélisation"]
    hdr_cells8 = table8.rows[0].cells
    for i, header in enumerate(headers8):
        hdr_cells8[i].text = header
        set_cell_background(hdr_cells8[i], "04A264")
        hdr_cells8[i].width = column_widths8[i]
        for p in hdr_cells8[i].paragraphs:
            p.alignment = 1

    # # Contenu
    def populate_table(table, suggestions, column_widths):
        """
        Populates the table with data from the suggestions list.

        Args:
            table (Table): The table to populate.
            suggestions (list): A list of suggestions, where each suggestion is a dictionary
                                with "canal" and "suggestion" keys.
            column_widths (list): A list of column widths in inches.
        """
        for i, item in enumerate(suggestions, 1):
            row_cells = table.rows[i].cells
            row_cells[0].text = item["canal"]
            row_cells[1].text = item["suggestion"]
            row_cells[0].width = column_widths[0]
            row_cells[1].width = column_widths[1]
            for cell in row_cells:
                for p in cell.paragraphs:
                    p.alignment = 1

    populate_table(table8, filtered_suggestions, column_widths8)

    # # Centrer le tableau
    tbl8 = table8._tbl
    tblPr8 = tbl8.tblPr
    if tblPr8 is None:
        tblPr8 = OxmlElement('w:tblPr')
        tbl8.insert(0, tblPr8)
    jc8 = OxmlElement('w:jc')
    jc8.set(qn('w:val'), 'center')
    tblPr8.append(jc8)

    # # Ajouter au contexte
    context_tables["texte2_page9"] = subdoc8
    context.update(context_tables)

    # Génération de la présentation du marché
    prompt_marche = f"""
    Voici les informations sur le projet : {json_data}

    Rédige une section d'analyse commerciale sous forme d’un seul paragraphe fluide et cohérent, intégrant naturellement les éléments suivants : 
    1. Positionnement stratégique (comment le projet se démarque),
    2. Présentation du marché marocain (définition, taille, tendances, besoins clients),
    3. Importance des canaux de ventes et de distribution,
    4. Zone géographique d’implantation au Maroc,
    5. Clients visés par le projet,
    6. Principaux concurrents sur le marché,
    7. Risques potentiels liés au projet,
    8. Modèle économique (business model),
    9. Stratégie marketing envisagée.

    Le ton doit être professionnel, clair et concis. Le texte doit rester synthétique tout en intégrant tous les éléments de manière naturelle dans un seul paragraphe.
    """
    # # Requête à Gemini
    try:
        response_marche = model.generate_content(prompt_marche)
        texte_marche = response_marche.text.strip()
    except Exception as e:
        print(f"❌ Erreur génération marché : {str(e)}")
        texte_marche = "Présentation du marché non disponible pour le moment veuillez l'ajouter."

    # Ajout au contexte
    context["texte1_page10"] = RichText(texte_marche)

    #client cible 
    def generate_client_target_description(data):
        """
        Génère une liste claire et structurée des segments de clients cibles à partir de notre data.
        """
        prompt_cible = f"""
        Tu es un expert en rédaction de business plans et en stratégie marketing.

        À partir des données suivantes : {data}, identifie UNIQUEMENT les segments de clients cibles.

        Consignes :
        - Donne uniquement des catégories de clients (par ex. : Étudiants universitaires, PME locales, etc.).
        - Utilise une liste à puces (tiret -).
        - Pas de phrases complètes.
        - AUCUNE phrase d’introduction, conclusion 
        - Maximum 6 à 9 mots par ligne.
        - Ne donne AUCUNE information sur le produit ou le projet.

        Format attendu :
        - Client type 1 
        - Client type 2  
        - Client type 3  
        - ...
        """
    
        response_cible = model.generate_content(prompt_cible)
        return response_cible.text.strip() if response_cible else "Client cible non défini."


    # Générer le texte du client cible avec Gemini
    client_target_text = generate_client_target_description(json_data)

    for paragraph in doc.paragraphs:
        if "{{texte2_page10}}" in paragraph.text:
            # Remplacer le texte cible
            paragraph.clear()
            paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
            paragraph.add_run(client_target_text)

    # Générer des suggestions de clients

    def generate_client_suggestions(data):
        """
        Génère des suggestions de clients non mentionnés dans la data.
        """
        prompt_suggestions = f"""
        Tu es un assistant marketing intelligent.
        À partir des données suivantes : {data}, propose 3 à 5 types de clients potentiels 
        qui ne sont PAS mentionnés explicitement, mais qui pourraient être intéressés par ce projet.
        
        -  Utilise une liste à puces (tiret -).
        - Maximum 10 mots par point.
        Format attendu :
        - Client type 1 
        - Client type 2  
        - Client type 3  
        - ...
        - Ne répète pas les clients déjà dans les données.
        -pas de répètition
        """

        response_sugg = model.generate_content(prompt_suggestions)
        return response_sugg.text.strip() if response_sugg else "Aucune suggestion trouvée."

    # --- Génération du contenu ---
    client_target_text = generate_client_target_description(json_data)
    client_suggestion_text = generate_client_suggestions(json_data)

    # --- Remplacement dans {{texte2_page10}} : Clients cibles ---
    for paragraph in doc.paragraphs:
        if "{{texte2_page10}}" in paragraph.text:
            paragraph.clear()
            paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
            paragraph.add_run(client_target_text)

    # --- Remplacement dans {{texte3_page10}} : Suggestions Akkan ---
    for paragraph in doc.paragraphs:
        if "{{texte3_page10}}" in paragraph.text:
            paragraph.clear()
            paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT

            # Texte suggestions
            paragraph.add_run(client_suggestion_text)

    # --- Mise à jour du contexte si utilisé ailleurs ---
    context["texte2_page10"] = RichText(client_target_text)
    context["texte3_page10"] = RichText(client_suggestion_text)

    # Prompt SWOT
    prompt_swot = f"""
    Analyse le projet suivant et génère une analyse SWOT très concise : {json_data}
    IMPORTANT :
    1. Ta réponse doit être UNIQUEMENT un objet JSON valide
    2. Chaque point DOIT faire maximum 5-6 mots
    3. Sois direct et précis
    4. Pas de phrases, juste des points clés

    Format exact à respecter :
    {{
        "forces": [
            "Premier point fort du projet",
            "Deuxième point fort du projet",
            "Troisième point fort du projet"
            "QUATRIEME point fort du projet"
            "CINQUIEME point fort du projet"
        ],
        "faiblesses": [
            "Première faiblesse du projet",
            "Deuxième faiblesse du projet",
            "Troisième faiblesse du projet"
            "QUATRIEME faiblesse du projet"
            "CINQUIEME faiblesse du projet"
        ],
        "opportunites": [
            "Première opportunité pour le projet",
            "Deuxième opportunité pour le projet",
            "Troisième opportunité pour le projet"
            "QUATRIEME opportunité pour le projet"
            "CINQUIEME opportunité pour le projet"
        ],
        "menaces": [
            "Première menace pour le projet",
            "Deuxième menace pour le projet",
            "Troisième menace pour le projet"
            "QUATRIEME menace pour le projet"
            "CINQUIEME menace pour le projet"
        ]
    }}
    """

    response_swot = model.generate_content(prompt_swot)
    swot_text = response_swot.text.strip()

    # Formatage texte
    def format_points(points):
        if isinstance(points, list) and points:
            return '\n'.join(f"- {point}" for point in points[:5])
        return "- Point non disponible\n- Point non disponible\n- Point non disponible"

    # Initialisation des valeurs
    texte1_page11 = texte2_page11 = texte3_page11 = texte4_page11 = ""

    # Extraction et parsing
    try:
        json_start = swot_text.find('{')
        json_end = swot_text.rfind('}') + 1
        if json_start != -1 and json_end != -1:
            clean_json = swot_text[json_start:json_end]
            swot_data = json.loads(clean_json)

            texte1_page11 = format_points(swot_data.get('forces', []))
            texte2_page11 = format_points(swot_data.get('faiblesses', []))
            texte3_page11 = format_points(swot_data.get('opportunites', []))
            texte4_page11 = format_points(swot_data.get('menaces', []))

            print("✅ Analyse SWOT générée et prête pour le template")
        else:
            raise ValueError("Aucun JSON valide trouvé dans la réponse.")
    except Exception as e:
        print(f"❌ Erreur lors du parsing JSON : {str(e)}")
        print("Réponse brute :", swot_text)
        
        # Valeurs par défaut
        default_points = "- Point non disponible\n- Point non disponible\n- Point non disponible"
        texte1_page11 = texte2_page11 = texte3_page11 = texte4_page11 = default_points

    # Stockage dans le dictionnaire contextuel
    context_tables = {
        "texte1_page11": texte1_page11,
        "texte2_page11": texte2_page11,
        "texte3_page11": texte3_page11,
        "texte4_page11": texte4_page11
    }
    context.update(context_tables)

    # Fonction de formatage des points
    def format_points(points):
        if isinstance(points, list) and points:
            return '\n'.join(f"- {point}" for point in points[:5])
        return "- Point non disponible\n- Point non disponible\n- Point non disponible"

    # Prompt PESTEL
    prompt_pestel = f"""
    Analyse le projet suivant et génère une analyse PESTEL très concise : {json_data}

    IMPORTANT :
    1. Ta réponse doit être UNIQUEMENT un objet JSON valide
    2. Chaque point DOIT faire maximum 5-6 mots
    3. Sois direct et précis
    4. Pas de phrases, juste des points clés

    Format exact à respecter :
    {{
        "politique": [
            "Premier facteur politique",
            "Deuxième facteur politique",
            "Troisième facteur politique",
            "QUATRIEME facteur politique",
            "CINQUIEME facteur politique"
        ],
        "economique": [
            "Premier facteur économique",
            "Deuxième facteur économique",
            "Troisième facteur économique",
            "QUATRIEME facteur économique",
            "CINQUIEME facteur économique"
        ],
        "social": [
            "Premier facteur social",
            "Deuxième facteur social",
            "Troisième facteur social",
            "QUATRIEME facteur social",
            "CINQUIEME facteur social"
        ],
        "technologique": [
            "Premier facteur technologique",
            "Deuxième facteur technologique",
            "Troisième facteur technologique",
            "QUATRIEME facteur technologique",
            "CINQUIEME facteur technologique"
        ],
        "environnemental": [
            "Premier facteur environnemental",
            "Deuxième facteur environnemental",
            "Troisième facteur environnemental",
            "QUATRIEME facteur environnemental",
            "CINQUIEME facteur environnemental"
        ],
        "legal": [
            "Premier facteur légal",
            "Deuxième facteur légal",
            "Troisième facteur légal",
            "QUATRIEME facteur légal",
            "CINQUIEME facteur légal"
        ]
    }}
    """

    # Génération avec le modèle
    response_pestel = model.generate_content(prompt_pestel)
    pestel_text = response_pestel.text.strip()

    # Initialisation
    texte1_page12 = texte2_page12 = texte3_page12 = texte4_page12 = texte5_page12 = texte6_page12 = ""

    # Extraction et parsing
    try:
        json_start = pestel_text.find('{')
        json_end = pestel_text.rfind('}') + 1
        if json_start != -1 and json_end != -1:
            clean_json = pestel_text[json_start:json_end]
            pestel_data = json.loads(clean_json)

            texte1_page12 = format_points(pestel_data.get('politique', []))
            texte2_page12 = format_points(pestel_data.get('economique', []))
            texte3_page12 = format_points(pestel_data.get('social', []))
            texte4_page12 = format_points(pestel_data.get('technologique', []))
            texte5_page12 = format_points(pestel_data.get('environnemental', []))
            texte6_page12 = format_points(pestel_data.get('legal', []))

            print("✅ Analyse PESTEL générée et prête pour le template")
        else:
            raise ValueError("Aucun JSON valide trouvé dans la réponse.")
    except Exception as e:
        print(f"❌ Erreur lors du parsing JSON : {str(e)}")
        print("Réponse brute :", pestel_text)
        
        # Valeurs par défaut
        default_points = "- Point non disponible\n- Point non disponible\n- Point non disponible"
        texte1_page12 = texte2_page12 = texte3_page12 = texte4_page12 = texte5_page12 = texte6_page12 = default_points

    # Stockage dans le dictionnaire contextuel
    context_tables = {
        "texte1_page12": texte1_page12,  # Politique
        "texte2_page12": texte2_page12,  # Économique
        "texte3_page12": texte3_page12,  # Social
        "texte4_page12": texte4_page12,  # Technologique
        "texte5_page12": texte5_page12,  # Environnemental
        "texte6_page12": texte6_page12   # Légal
    }
    context.update(context_tables)

    # equipe
    data_equipe = json_data.get('equipes')
    prompt_equipe = f"""
    Tu es un assistant expert en rédaction de contenu pour les business plans.

    À partir de la liste D'equipe dans {data_equipe}, génère un paragraphe clair et professionnel décrivant chaque poste avec un résumé de ses rôles et responsabilités .

    Utilise des phrases complètes, sans liste à puces. Sois concis mais informatif. Si une fonction n’est pas courante, ajoute une brève explication de son rôle.


    Génère uniquement le paragraphe sans autre commentaire.
    """

    # Appel à l'API Gemini
    response = model.generate_content(prompt_equipe)

    # Contenu récupéré depuis Gemini
    texte1_page13 = response.text.strip()

    # Stockage dans le contexte pour docxtpl
    context.update({
        "texte1_page13": texte1_page13
    })
    #gestion des operations 
    prompt_operation= f"""
    Tu es un assistant expert en rédaction de contenu pour les business plans.
    a partir de {json_data} pour l'equipe {data_equipe} genere un paragraphe decrivant Plan de gestion des opérations
    Sans phrases d'introduction ou de conclusion.
    Ne fais pas de répétitions.
    Le ton doit rester professionnel et adapté à un document de type business plan.
    Ne depasse pas 10lignes

    """
    # Appel à l'API Gemini
    response = model.generate_content(prompt_operation)
    # Contenu récupéré depuis Gemini
    texte2_page13 = response.text.strip()
    # Stockage dans le contexte pour docxtpl
    context.update({
        "texte2_page13": texte2_page13
    })

    #gestion financiere
    prompt_finance = f"""
    Tu es un assistant expert en rédaction de business plan.

    Voici les données détaillées du projet  : 
    {json_data}

    Génère un **paragraphe unique et spécifique** décrivant **le plan de gestion financière** de ce projet **en te basant uniquement sur les informations présentes dans ces données**. N'invente rien.

    Le paragraphe doit inclure :
    - Le nom ou poste exact de la ou des personnes responsables de la gestion des finances dans cette équipe,
    - Les outils ou logiciels mentionnés ou que cette équipe est susceptible d’utiliser selon les données,
    - Comment la comptabilité est tenue selon les données (si ce n'est pas précisé,ne la  mentionne pas ).

    Le ton doit rester professionnel et adapté à un document de type business plan.
    """
    # Appel à l'API Gemini  
    response = model.generate_content(prompt_finance)   
    # Contenu récupéré depuis Gemini
    texte1_page14 = response.text.strip()
    # Stockage dans le contexte pour docxtpl
    context.update({
        "texte1_page14": texte1_page14
    })
    #gestion des riques
    prompt_risque = f"""Tu es un expert en analyse de risques pour projets entrepreneuriaux.  
    À partir des données suivantes concernant le projet : {json_data},  
    rédige un paragraphe expliquant les principales mesures prises pour identifier et gérer les risques liés à ce projet.  
    Sois précis et adapte ta réponse au contexte du projet.
    ne depasse pas 10lignes
    Ne fais pas de phrases d'introduction ou de conclusion.
    Ne fais pas de répétitions.

    """
    # Appel à l'API Gemini
    response = model.generate_content(prompt_risque)
    # Contenu récupéré depuis Gemini
    texte2_page14 = response.text.strip()
    # Stockage dans le contexte pour docxtpl
    context.update({
        "texte2_page14": texte2_page14
    })
    #gestion des risques
    prompt_impact = f"""Tu es un consultant en gestion de crise.  
    À partir des données suivantes concernant le projet : {json_data},  
    génère un paragraphe décrivant les plans d'urgence prévus pour assurer la continuité des activités en cas de problèmes majeurs ou imprévus.  
    Ta réponse doit être adaptée au type d’activité décrite.
    ne depasse pas 10lignes
    Ne fais pas de phrases d'introduction ou de conclusion.
    Ne fais pas de répétitions.


    """
    # Appel à l'API Gemini
    response = model.generate_content(prompt_impact)
    # Contenu récupéré depuis Gemini
    texte3_page14 = response.text.strip()
    # Stockage dans le contexte pour docxtpl
    context.update({
        "texte3_page14": texte3_page14
    })
    apports = json_data.get("apports", [])

    if not apports:
        print("⚠️ Aucun apport trouvé.")
    else:
        print("✅ Apports disponibles :")
        for a in apports:
            nature = a.get("id_nature", "").strip()
            montant = a.get("amount", 0)
            date_apport = a.get("apport_at", "")
            print(f" - {nature} : {montant} DH le {date_apport}")
    # 2. Calculer les montants
    numeraire = sum(float(a["amount"]) if "amount" in a and isinstance(a["amount"], (int, float, str)) else 0 for a in apports if a["is_personal"])
    nature = sum(float(a["amount"]) if "amount" in a and isinstance(a["amount"], (int, float, str)) else 0 for a in apports if not a["is_personal"])

    print(f"numeraire: {numeraire}")
    print(f"nature: {nature}")

    # 3. Vérifier si la somme des apports est nulle
    if numeraire + nature > 0:
        labels = ['Apports en numéraire', 'Apports en nature']
        amounts = [numeraire, nature]
        colors = ['#4CAF50', '#2d4d31']

        plt.figure(figsize=(6, 6))
        plt.pie(amounts, labels=labels, autopct='%1.1f%%', startangle=90, colors=colors)
        plt.title("Répartition des apports entre les actionnaires")
        plt.axis('equal')
        plt.savefig("camembert_apports.png", bbox_inches='tight')
        plt.close()

        # 4. Préparer le contexte avec l’image
        image_path = "camembert_apports.png"
        image = InlineImage(doc, image_path, width=Cm(12))  # largeur adaptée

        context['texte1_page15'] = image
    else:
        print("Erreur : la somme des apports est nulle. Vérifiez les données.")
        context['texte1_page15'] = "Aucun apport n'a été effectué."
    # 5. Générer une petite description textuelle 
    if numeraire + nature > 0:
        prompt = (
            f"Voici les apports d'un projet :\n"
            f"- Apports en numéraire : {numeraire} DH\n"
            f"- Apports en nature : {nature} DH\n\n"
            f"Génère un paragraphe synthétique en français qui décrit ces apports et leur répartition."
        )

        # Appel à l'API Gemini
        response = model.generate_content(prompt)
        texte_description = response.text.strip()
        
        context['texte2_page15'] = texte_description
    else:
        context['texte2_page15'] = "Aucun apport n'a été effectué pour ce projet."

    #INVESTISSEMENT

    # Récupération des données investissements
    investissements_data = json_data.get("Investissements", [])
    volume_vente_data = json_data.get('volume_vente', [])
    #investissements_data = []
    for item in volume_vente_data:
        if isinstance(item, dict) and 'Investissements' in item:
            investissements_data = item['Investissements']
            print(investissements_data)
            break



    # Création du sous-document
    subdoc_investissements = doc.new_subdoc()

    # Création du tableau : 3 colonnes
    table_invest = subdoc_investissements.add_table(rows=len(investissements_data) + 1, cols=3)
    table_invest.style = "Table Grid"

    # Largeurs des colonnes
    column_widths_invest = [Inches(3.5), Inches(2.0), Inches(1.5)]  # Investissements, Valeur, Apport

    # Entêtes
    hdr_cells = table_invest.rows[0].cells
    headers = ['Investissements', 'Valeur (TTC)', 'Apport']
    for i, header in enumerate(headers):
        hdr_cells[i].text = header
        hdr_cells[i].width = column_widths_invest[i]

        # Fond vert pour l'en-tête
        set_cell_background(hdr_cells[i], "04A264")  

    # Remplissage du tableau
    for i, item in enumerate(investissements_data, 1):
        row_cells = table_invest.rows[i].cells
        row_cells[0].text = str(item.get("name", ""))
        row_cells[1].text = str(item.get("amount", ""))
        row_cells[2].text = "Oui" if item.get("isApport", False) else "Non"  # plus lisible que True/False

        for j in range(3):
            row_cells[j].width = column_widths_invest[j]

    # Centrage du tableau
    tbl = table_invest._tbl
    tblPr = tbl.tblPr
    if tblPr is None:
        tblPr = OxmlElement('w:tblPr')
        tbl.insert(0, tblPr)

    jc = OxmlElement('w:jc')
    jc.set(qn('w:val'), 'center')
    tblPr.append(jc)

    #  Ajout dans le contexte
    context["texte1_page16"] = subdoc_investissements

    for i, item in enumerate(investissements_data):
        print(f"Ligne {i + 1} - name: {item.get('name')}, amount: {item.get('amount')}, isApport: {item.get('isApport')}")
    #graphe des financements
    # Récupérer les données de ressources
    ressources_data = json_data.get("ressources_data", {})

    # Récupérer les listes de financement
    subvention_list = json_data.get("subvention", [])
    Pret_honneur_list = json_data.get("Pret_honneur", [])
    Pret_bancaire_list = json_data.get("Pret_bancaire", [])

    # Calcul des montants
    subvention = sum(item.get("amount", 0) for item in subvention_list)
    Pret_honneur = sum(item.get("amount", 0) for item in Pret_honneur_list)
    Pret_bancaire = sum(item.get("amount", 0) for item in Pret_bancaire_list)
    Crowdfunding_akkan = ressources_data.get("Crowdfunding_akkan", 0.0)
    Apports = ressources_data.get("Apports_des_associes", 0.0)



    for item in volume_vente_data:
        if isinstance(item, dict) and 'subvention' in item:
            subvention  = item['subvention']
            if isinstance(subvention, list):
                subvention = sum(s.get("montant", 0) for s in subvention if isinstance(s, dict))
            else:
                subvention = subvention.get("montant", 0)
            break

    print(f"subvention : {subvention }")
    for item in volume_vente_data:
        if isinstance(item, dict) and 'Pret_honneur' in item:
            Pret_honneur = item['Pret_honneur']
            if isinstance(Pret_honneur, list):
                Pret_honneur = sum(p.get("montant", 0) for p in Pret_honneur if isinstance(p, dict))
            else:
                Pret_honneur = Pret_honneur.get("montant", 0)
            break
    print(f"pret_honneur : {Pret_honneur }")
    for item in volume_vente_data:
        if isinstance(item, dict) and 'Pret_bancaire' in item:
            Pret_bancaire = item['Pret_bancaire']
            if isinstance(Pret_bancaire, list):
                Pret_bancaire = sum(p.get("montant", 0) for p in Pret_bancaire if isinstance(p, dict))
            else:
                Pret_bancaire = Pret_bancaire.get("montant", 0)
            break
    print(f"pret_bancaire : {Pret_bancaire }")
    for item in volume_vente_data:
        if isinstance(item, dict) and 'fundraising' in item:
            Crowdfunding_akkan = item.get('fundraising',{})
            if isinstance(Crowdfunding_akkan, dict):
                Crowdfunding_akkan = Crowdfunding_akkan.get('total_amount', 0)
            break # Assuming you only need the first one found 
    print(f"Crowdfunding Akkan : {Crowdfunding_akkan }")

    # 1. Extraire les données de financement depuis le JSON
    """ subvention = json_data.get("subvention", [])
    pret_honneur = json_data.get("Pret_honneur", [])
    pret_bancaire = json_data.get("Pret_bancaire", [])
    crowdfunding = json_data.get("Crowdfunding_akkan", []) """

    # 2. Fonction pour convertir les montants (chaînes) en float

    def total_funding(liste):
        return sum(
            float(item.get("montant", 0))
            for item in liste
            if isinstance(item, dict)
        )

    # Appliquer la fonction corrigée
    Subvention = total_funding(json_data.get("subvention", []))
    Pret_honneur = total_funding(json_data.get("Pret_honneur", []))
    Pret_bancaire = total_funding(json_data.get("Pret_bancaire", []))
    Crowdfunding_akkan = json_data.get("fundraising", {}).get("total_amount", 0)

    # Labels et valeurs
    funding_data = [
        ('Crowdfunding Akkan', Crowdfunding_akkan),
        ('Prêt bancaire', Pret_bancaire),
        ('Prêt d\'honneur', Pret_honneur),
        ('Subvention', Subvention)
    ]

    # Filtrer les sources de financement avec une valeur non nulle
    filtered_funding_data = [(label, value) for label, value in funding_data if value > 0]

    funding_labels = [label for label, _ in filtered_funding_data]
    funding_values = [value for _, value in filtered_funding_data]

    funding_values_sum = sum(funding_values)

    # 5. Générer le graphique si des financements sont renseignés
    if funding_values_sum > 0:
        base_colors = ['#1f4927', '#287236', '#4CAF50', '#93ffa8']
        colors = [base_colors[i % len(base_colors)] for i in range(len(funding_values))]
        plt.figure(figsize=(6, 6))
        plt.pie(funding_values, labels=funding_labels, autopct='%1.1f%%', startangle=90, colors=colors)
        plt.title("Répartition des sources de financement")
        plt.axis('equal')
        plt.savefig("camembert_financement.png", bbox_inches='tight')
        plt.close()

        # 6. Insérer dans le document Word
        image_path2 = "camembert_financement.png"
        image2 = InlineImage(doc, image_path2, width=Cm(12))
        context['texte2_page16'] = image2
    else:
        print("Erreur : aucun financement trouvé.")
        context['texte2_page16'] = "Aucun financement renseigné."
    #JALON
    jalons = json_data.get("jalons", [])
    print(f"jalons: {jalons}")
    # Construire une liste de dictionnaires (une par ligne de tableau)
    table_data = []

    for idx, jalon in enumerate(jalons, start=1):
        periode = f"{jalon['term']} Jours" if jalon.get('term') else "T0 (lancement)"
        nom_etape = jalon.get('name') or jalon.get('description', f"Jalon {idx}")
        montant = f"{jalon['amount']:,.2f} DH" if jalon.get('amount') else "Montant non spécifié"
        utilisation = jalon.get('description', "Non spécifié")
        
        # Récupérer et formater la date de début
        date_debut_str = jalon.get('date_delivrable', '')
        date_debut_formatted = date_debut_str.split('T')[0] if date_debut_str else 'Non spécifiée'
        
        table_data.append({
            "jalon": str(idx),
            "date_debut": date_debut_formatted,
            "periode": periode,
            "montant": montant,
            "utilisation": utilisation
        })


    # Créer le tableau dans le document
    subdoc = doc.new_subdoc()
    table = subdoc.add_table(rows=len(table_data) + 1, cols=5)
    table.style = "Table Grid"
    column_widths = [Inches(1), Inches(2), Inches(2), Inches(2), Inches(3)]
    # En-têtes
    headers = ["Jalon", "Date de début", "Période", "Montant", "Utilisation"]
    hdr_cells = table.rows[0].cells
    for i, header in enumerate(headers):
        hdr_cells[i].text = header
        set_cell_background(hdr_cells[i], "04A264")  # Fonction que tu utilises déjà
        hdr_cells[i].width = column_widths[i]
        for p in hdr_cells[i].paragraphs:
            p.alignment = 1
    # Remplissage du tableau
    for i, item in enumerate(table_data, 1):
        row_cells = table.rows[i].cells
        row_cells[0].text = item["jalon"]
        row_cells[1].text = item["date_debut"]
        row_cells[2].text = item["periode"]
        row_cells[3].text = item["montant"]
        row_cells[4].text = item["utilisation"]

        for j in range(5):
            row_cells[j].width = column_widths[j]
            for p in row_cells[j].paragraphs:
                p.alignment = 1
    # Centrer le tableau
    tbl = table._tbl
    tblPr = tbl.tblPr
    if tblPr is None:
        tblPr = OxmlElement('w:tblPr')
        tbl.insert(0, tblPr)
    jc = OxmlElement('w:jc')
    jc.set(qn('w:val'), 'center')
    tblPr.append(jc)
    # Ajouter le tableau au contexte
    context_tables = {
        "texte1_page17": subdoc
    }


    # Déblocage des fonds (Horizontal Bar Chart)
    jalons_data = json_data.get("jalons", [])
    periods = []
    amounts = []

    for jalon in jalons_data:
        if jalon.get('amount') is not None:
            periode_label = f"{jalon['term']} Jours" if jalon.get('term') else "T0 (lancement)"
            periods.append(periode_label)
            amounts.append(float(jalon['amount']))

    # Combine and sort data by amount
    combined_data = sorted(zip(amounts, periods), key=lambda x: x[0])
    sorted_amounts = [item[0] for item in combined_data]
    sorted_periods = [item[1] for item in combined_data]  # "3 Jours", etc.

    if sorted_amounts:
        plt.figure(figsize=(12, 8))
        x_pos = np.arange(len(sorted_amounts)) # Use indices for x-positions
        
        bars = plt.bar(x_pos, [float(p.split()[0]) for p in sorted_periods], color='#4CAF50')
        plt.xlabel("Montant (DH)", fontsize=12)
        plt.ylabel("Durée (jours)", fontsize=12)
        plt.title("Déblocage des fonds", fontsize=14)

        # Set x-tick labels to actual amounts
        plt.xticks(x_pos, sorted_amounts, rotation=45, ha='right') # Rotate for readability

        # Add labels on top of each bar
        for bar, label in zip(bars, sorted_periods):
            plt.text(bar.get_x() + bar.get_width() / 2, bar.get_height(),
                     label, ha='center', va='bottom', fontsize=10)

        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig("deblocage_fonds.png", bbox_inches='tight')
        plt.close()

        image_path_deblocage = "deblocage_fonds.png"
        image_deblocage = InlineImage(doc, image_path_deblocage, width=Cm(15))
        context_tables["texte2_page17"] = image_deblocage
    else:
        print("Aucune donnée de jalon avec montant pour le graphique de déblocage des fonds.")
        context_tables["texte2_page17"] = "Aucune donnée de déblocage des fonds disponible."

    context.update(context_tables)

    # Prix de Vente Table
    subdoc_prix_vent = doc.new_subdoc()
    data_prix_vent = json_data.get('prix_vent', [])

    # Calculate totals
    total_prix_vente = sum(item.get('prix_vente', 0) for item in data_prix_vent)
    total_tva_vente = sum(item.get('tva_vente', 0) for item in data_prix_vent)
    total_prix_vente_ttc = sum(item.get('prix_vente_ttc', 0) for item in data_prix_vent)
    total_revenues_ht = sum(item.get('Revenues_HT', 0) for item in data_prix_vent)

    # Create the table
    table_prix_vent = subdoc_prix_vent.add_table(rows=len(data_prix_vent) + 2, cols=5)
    table_prix_vent.style = "Table Grid"

    # Define column widths
    column_widths_prix_vent = [Inches(1.5), Inches(3.0), Inches(1.0), Inches(0.7), Inches(1.3)]

    # Headers
    hdr_cells_prix_vent = table_prix_vent.rows[0].cells
    headers_prix_vent = ['Offre', 'Description', 'Prix de ventes', 'TVA', 'Prix de vente TTC']
    for i, header in enumerate(headers_prix_vent):
        hdr_cells_prix_vent[i].text = header
        hdr_cells_prix_vent[i].width = column_widths_prix_vent[i]
        set_cell_background(hdr_cells_prix_vent[i], "04A264")
        for p in hdr_cells_prix_vent[i].paragraphs:
            p.alignment = 1

    # Populate data rows
    for i, item in enumerate(data_prix_vent, 1):
        row_cells = table_prix_vent.rows[i].cells
        row_cells[0].text = item.get("name", "")
        row_cells[1].text = item.get("description", "")
        row_cells[2].text = f"{item.get('prix_vente', 0):,.2f}"
        row_cells[3].text = f"{item.get('tva_vente', 0):.2f}%"
        row_cells[4].text = f"{item.get('prix_vente_ttc', 0):,.2f}"

        for j in range(5):
            row_cells[j].width = column_widths_prix_vent[j]
            for p in row_cells[j].paragraphs:
                p.alignment = 1

    # Add total row
    total_row_cells = table_prix_vent.rows[len(data_prix_vent) + 1].cells
    total_row_cells[0].text = "TOTAL"
    total_row_cells[1].text = f"{total_revenues_ht:,.2f} DH" # Assuming this is the total revenue
    total_row_cells[2].text = f"{total_prix_vente:,.2f}"
    total_row_cells[3].text = f"{total_tva_vente:.2f}%" # Sum of percentages might not be meaningful, but following example
    total_row_cells[4].text = f"{total_prix_vente_ttc:,.2f}"

    for j in range(5):
        total_row_cells[j].width = column_widths_prix_vent[j]
        for p in total_row_cells[j].paragraphs:
            p.alignment = 1

    # Center the table
    tbl_prix_vent = table_prix_vent._tbl
    tblPr_prix_vent = tbl_prix_vent.tblPr
    if tblPr_prix_vent is None:
        tblPr_prix_vent = OxmlElement('w:tblPr')
        tbl_prix_vent.insert(0, tblPr_prix_vent)

    jc_prix_vent = OxmlElement('w:jc')
    jc_prix_vent.set(qn('w:val'), 'center')
    tblPr_prix_vent.append(jc_prix_vent)

    # Add to context
    context_tables["texte1_page18"] = subdoc_prix_vent
    context.update(context_tables)

    # Coût de Production Table
    subdoc_cout_production = doc.new_subdoc()
    data_cout_production = json_data.get('prix_vent', []) # Using prix_vent as per user's request

    # Create the table
    table_cout_production = subdoc_cout_production.add_table(rows=len(data_cout_production) + 1, cols=5)
    table_cout_production.style = "Table Grid"

    # Define column widths
    column_widths_cout_production = [Inches(1.5), Inches(3.0), Inches(1.0), Inches(0.7), Inches(1.3)]

    # Headers
    hdr_cells_cout_production = table_cout_production.rows[0].cells
    headers_cout_production = ['Offre', 'Description', 'Coût HT', 'TVA', 'Coût TTC'] # Changed last "Coût HT" to "Coût TTC" for clarity
    for i, header in enumerate(headers_cout_production):
        hdr_cells_cout_production[i].text = header
        hdr_cells_cout_production[i].width = column_widths_cout_production[i]
        set_cell_background(hdr_cells_cout_production[i], "04A264")
        for p in hdr_cells_cout_production[i].paragraphs:
            p.alignment = 1

    # Populate data rows
    for i, item in enumerate(data_cout_production, 1):
        row_cells = table_cout_production.rows[i].cells
        row_cells[0].text = item.get("name", "")
        row_cells[1].text = item.get("description", "")
        row_cells[2].text = f"{item.get('cout_unitaire', 0):,.2f}"
        row_cells[3].text = f"{item.get('tva_achat', 0):.2f}%"
        row_cells[4].text = f"{item.get('cout_unitaire_ttc', 0):,.2f}"

        for j in range(5):
            row_cells[j].width = column_widths_cout_production[j]
            for p in row_cells[j].paragraphs:
                p.alignment = 1

    # Center the table
    tbl_cout_production = table_cout_production._tbl
    tblPr_cout_production = tbl_cout_production.tblPr
    if tblPr_cout_production is None:
        tblPr_cout_production = OxmlElement('w:tblPr')
        tbl_cout_production.insert(0, tblPr_cout_production)

    jc_cout_production = OxmlElement('w:jc')
    jc_cout_production.set(qn('w:val'), 'center')
    tblPr_cout_production.append(jc_cout_production)

    # Add to context
    context_tables["texte2_page18"] = subdoc_cout_production
    context.update(context_tables)

    # Volumes de Ventes Basse Table
    subdoc_ventes_basse = doc.new_subdoc()
    data_ventes_basse = json_data.get('prix_vent', []) # Using prix_vent as per user's request

    # Create the table
    table_ventes_basse = subdoc_ventes_basse.add_table(rows=len(data_ventes_basse) + 1, cols=5)
    table_ventes_basse.style = "Table Grid"

    # Define column widths
    column_widths_ventes_basse = [Inches(1.5), Inches(1.0), Inches(0.7), Inches(1.5), Inches(1.5)]

    # Headers
    hdr_cells_ventes_basse = table_ventes_basse.rows[0].cells
    headers_ventes_basse = ['Offre', 'Quantité', 'TVA', 'Revenus HT', 'Revenus TTC']
    for i, header in enumerate(headers_ventes_basse):
        hdr_cells_ventes_basse[i].text = header
        hdr_cells_ventes_basse[i].width = column_widths_ventes_basse[i]
        set_cell_background(hdr_cells_ventes_basse[i], "04A264")
        for p in hdr_cells_ventes_basse[i].paragraphs:
            p.alignment = 1

    # Populate data rows
    for i, item in enumerate(data_ventes_basse, 1):
        row_cells = table_ventes_basse.rows[i].cells
        row_cells[0].text = item.get("name", "")
        row_cells[1].text = str(item.get("s_basse_nombre_vente", 0)) # Use s_basse_nombre_vente for quantity
        row_cells[2].text = f"{item.get('tva_vente', 0):.2f}%"
        row_cells[3].text = f"{item.get('Revenues_HT', 0):,.2f}"
        row_cells[4].text = f"{item.get('Revenues_TTC', 0):,.2f}"

        for j in range(5):
            row_cells[j].width = column_widths_ventes_basse[j]
            for p in row_cells[j].paragraphs:
                p.alignment = 1

    # Center the table
    tbl_ventes_basse = table_ventes_basse._tbl
    tblPr_ventes_basse = tbl_ventes_basse.tblPr
    if tblPr_ventes_basse is None:
        tblPr_ventes_basse = OxmlElement('w:tblPr')
        tbl_ventes_basse.insert(0, tblPr_ventes_basse)

    jc_ventes_basse = OxmlElement('w:jc')
    jc_ventes_basse.set(qn('w:val'), 'center')
    tblPr_ventes_basse.append(jc_ventes_basse)

    # Add to context
    context_tables["texte1_page19"] = subdoc_ventes_basse
    context.update(context_tables)

    # Volumes de Ventes Moyenne Table
    subdoc_ventes_moyenne = doc.new_subdoc()
    data_ventes_moyenne = json_data.get('prix_vent', []) # Using prix_vent as per user's request

    # Create the table
    table_ventes_moyenne = subdoc_ventes_moyenne.add_table(rows=len(data_ventes_moyenne) + 1, cols=5)
    table_ventes_moyenne.style = "Table Grid"

    # Define column widths
    column_widths_ventes_moyenne = [Inches(1.5), Inches(1.0), Inches(0.7), Inches(1.5), Inches(1.5)]

    # Headers
    hdr_cells_ventes_moyenne = table_ventes_moyenne.rows[0].cells
    headers_ventes_moyenne = ['Offre', 'Quantité', 'TVA', 'Revenus HT', 'Revenus TTC']
    for i, header in enumerate(headers_ventes_moyenne):
        hdr_cells_ventes_moyenne[i].text = header
        hdr_cells_ventes_moyenne[i].width = column_widths_ventes_moyenne[i]
        set_cell_background(hdr_cells_ventes_moyenne[i], "04A264")
        for p in hdr_cells_ventes_moyenne[i].paragraphs:
            p.alignment = 1

    # Populate data rows
    for i, item in enumerate(data_ventes_moyenne, 1):
        row_cells = table_ventes_moyenne.rows[i].cells
        row_cells[0].text = item.get("name", "")
        row_cells[1].text = str(item.get("nombre_vente", 0)) # Use nombre_vente for quantity
        row_cells[2].text = f"{item.get('tva_vente', 0):.2f}%"
        row_cells[3].text = f"{item.get('Revenues_HT', 0):,.2f}"
        row_cells[4].text = f"{item.get('Revenues_TTC', 0):,.2f}"

        for j in range(5):
            row_cells[j].width = column_widths_ventes_moyenne[j]
            for p in row_cells[j].paragraphs:
                p.alignment = 1

    # Center the table
    tbl_ventes_moyenne = table_ventes_moyenne._tbl
    tblPr_ventes_moyenne = tbl_ventes_moyenne.tblPr
    if tblPr_ventes_moyenne is None:
        tblPr_ventes_moyenne = OxmlElement('w:tblPr')
        tbl_ventes_moyenne.insert(0, tblPr_ventes_moyenne)

    jc_ventes_moyenne = OxmlElement('w:jc')
    jc_ventes_moyenne.set(qn('w:val'), 'center')
    tblPr_ventes_moyenne.append(jc_ventes_moyenne)

    # Add to context
    context_tables["texte2_page19"] = subdoc_ventes_moyenne
    context.update(context_tables)

    # Volumes de Ventes Haute Table
    subdoc_ventes_haute = doc.new_subdoc()
    data_ventes_haute = json_data.get('prix_vent', []) # Using prix_vent as per user's request

    # Create the table
    table_ventes_haute = subdoc_ventes_haute.add_table(rows=len(data_ventes_haute) + 1, cols=5)
    table_ventes_haute.style = "Table Grid"

    # Define column widths
    column_widths_ventes_haute = [Inches(1.5), Inches(1.0), Inches(0.7), Inches(1.5), Inches(1.5)]

    # Headers
    hdr_cells_ventes_haute = table_ventes_haute.rows[0].cells
    headers_ventes_haute = ['Offre', 'Quantité', 'TVA', 'Revenus HT', 'Revenus TTC']
    for i, header in enumerate(headers_ventes_haute):
        hdr_cells_ventes_haute[i].text = header
        hdr_cells_ventes_haute[i].width = column_widths_ventes_haute[i]
        set_cell_background(hdr_cells_ventes_haute[i], "04A264")
        for p in hdr_cells_ventes_haute[i].paragraphs:
            p.alignment = 1

    # Populate data rows
    for i, item in enumerate(data_ventes_haute, 1):
        row_cells = table_ventes_haute.rows[i].cells
        row_cells[0].text = item.get("name", "")
        row_cells[1].text = str(item.get("s_haute_nombre_vente", 0)) # Use s_haute_nombre_vente for quantity
        row_cells[2].text = f"{item.get('tva_vente', 0):.2f}%"
        row_cells[3].text = f"{item.get('Revenues_HT', 0):,.2f}"
        row_cells[4].text = f"{item.get('Revenues_TTC', 0):,.2f}"

        for j in range(5):
            row_cells[j].width = column_widths_ventes_haute[j]
            for p in row_cells[j].paragraphs:
                p.alignment = 1

    # Center the table
    tbl_ventes_haute = table_ventes_haute._tbl
    tblPr_ventes_haute = tbl_ventes_haute.tblPr
    if tblPr_ventes_haute is None:
        tblPr_ventes_haute = OxmlElement('w:tblPr')
        tbl_ventes_haute.insert(0, tblPr_ventes_haute)

    jc_ventes_haute = OxmlElement('w:jc')
    jc_ventes_haute.set(qn('w:val'), 'center')
    tblPr_ventes_haute.append(jc_ventes_haute)

    # Add to context
    context_tables["texte3_page19"] = subdoc_ventes_moyenne
    context.update(context_tables)

    # Volume des ventes Stacked Bar Chart
    data_volume_ventes = json_data.get('prix_vent', [])

    offer_names = []
    basse_percentages = []
    moyenne_percentages = []
    haute_percentages = []

    for item in data_volume_ventes:
        name = item.get('name', 'Offre Inconnue')
        basse = item.get('s_basse_nombre_vente', 0)
        moyenne = item.get('nombre_vente', 0)
        haute = item.get('s_haute_nombre_vente', 0)

        total_volume = basse + moyenne + haute
        if total_volume > 0:
            basse_percent = (basse / total_volume) * 100
            moyenne_percent = (moyenne / total_volume) * 100
            haute_percent = (haute / total_volume) * 100
        else:
            basse_percent = 0
            moyenne_percent = 0
            haute_percent = 0

        offer_names.append(name)
        basse_percentages.append(basse_percent)
        moyenne_percentages.append(moyenne_percent)
        haute_percentages.append(haute_percent)

    if offer_names:
        plt.figure(figsize=(12, 8))
        bar_width = 0.5
        indices = np.arange(len(offer_names))

        # Plotting bars
        plt.bar(indices, basse_percentages, bar_width, label='Basse', color='#93ffa8') # Blue
        plt.bar(indices, moyenne_percentages, bar_width, label='Moyenne', color='#2ca02c'  ,
                bottom=basse_percentages) # Orange
        plt.bar(indices, haute_percentages, bar_width, label='Haute', color='#287236',
                bottom=np.array(basse_percentages) + np.array(moyenne_percentages)) # Green

        plt.xlabel("Offre", fontsize=12)
        plt.ylabel("Pourcentage du Volume (%)", fontsize=12)
        plt.title("Volume des ventes", fontsize=14)
        plt.xticks(indices, offer_names, rotation=45, ha='right')
        plt.yticks(np.arange(0, 101, 20), [f'{i}%' for i in np.arange(0, 101, 20)]) # Y-axis in percentage
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, -0.2), ncol=3)
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig("volume_ventes_stacked.png", bbox_inches='tight')
        plt.close()

        image_path_volume_ventes = "volume_ventes_stacked.png"
        image_volume_ventes = InlineImage(doc, image_path_volume_ventes, width=Cm(15))
        context_tables["texte1_page20"] = image_volume_ventes
    else:
        print("Aucune donnée de volume de ventes disponible pour le graphique.")
        context_tables["texte1_page20"] = "Aucune donnée de volume de ventes disponible."

    context.update(context_tables)

    #equipe poste
    subdoc_equipes = doc.new_subdoc()
    data_equipes = json_data.get('equipes', [])

    # Regrouper les données par poste
    grouped = defaultdict(lambda: {"contract_type": None, "Annee 1": None, "Annee 2": None, "Annee 3": None})
    for item in data_equipes:
        post = item["post_name"].strip()
        grouped[post]["contract_type"] = item["contract_type"]
        annee = item["annee"]
        salaire = item["salaire_par_employe"].replace(" ", "").replace(",", ".")  # convertir en float
        grouped[post][annee] = float(salaire)



    # Préparer les données du tableau

    # The 'table' variable here is from the 'offre' section, which is not correct.
    # It should be a new table for 'equipes'.
    # I will define a new table for this section.
    # hdr_cells = table.rows[0].cells # This line will cause an error
    headers_equipes_table = ["Poste", "Type de contrat", "Année 1", "Année 2", "Année 3"] # Renamed to avoid conflict
    # for i, header in enumerate(headers): # This loop will cause an error
    #     hdr_cells[i].text = header
    #     hdr_cells[i].width = column_widths[i]
    #     hdr_cells = table.rows[0].cells
    #     set_cell_background(hdr_cells[i], "04A264")  # fond vert
    table_data = []
    total_1 = total_2 = total_3 = 0.0

    for post, info in grouped.items():
        annee1 = info["Annee 1"] or 0.0
        annee2 = info["Annee 2"] or annee1
        annee3 = info["Annee 3"] or annee2

        total_1 += annee1
        total_2 += annee2
        total_3 += annee3

        table_data.append([
            post,
            str(info["contract_type"]),
            f"{annee1:,.2f}".replace(",", " ").replace(".", ","),
            f"{annee2:,.2f}".replace(",", " ").replace(".", ","),
            f"{annee3:,.2f}".replace(",", " ").replace(".", ","),
        ])

    # Ajouter la ligne total
    table_data.append([
        "Total",
        "",
        f"{total_1:,.2f}".replace(",", " ").replace(".", ","),
        f"{total_2:,.2f}".replace(",", " ").replace(".", ","),
        f"{total_3:,.2f}".replace(",", " ").replace(".", ","),
    ])

    # Créer le tableau Word
    table_equipes = subdoc_equipes.add_table(rows=1, cols=len(headers_equipes_table)) # Renamed table variable
    table_equipes.style = 'Table Grid'
    table_equipes.autofit = True
    column_widths_equipes = [Inches(1.5), Inches(1.5), Inches(1.0), Inches(1.0), Inches(1.0)]

    # En-tête
    hdr_cells_equipes = table_equipes.rows[0].cells # Renamed hdr_cells variable
    for i, header in enumerate(headers_equipes_table):
        hdr_cells_equipes[i].text = header
        hdr_cells_equipes[i].width = column_widths_equipes[i]
        for paragraph in hdr_cells_equipes[i].paragraphs:
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        hdr_cells_equipes[i].vertical_alignment = WD_ALIGN_VERTICAL.CENTER
        # Fond vert pour l'en-tête
        set_cell_background(hdr_cells_equipes[i], "04A264")  # Fonction que tu utilises déjà

    # Lignes de données + total
    for row_data in table_data:
        row_cells = table_equipes.add_row().cells # Renamed table variable
        for i, val in enumerate(row_data):
            row_cells[i].text = val
            for paragraph in row_cells[i].paragraphs:
                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            row_cells[i].vertical_alignment = WD_ALIGN_VERTICAL.CENTER

    # Injection dans le contexte
    context_tables = {}
    context_tables["texte1_page21"] = subdoc_equipes
    # Charges salariales Line Graph
    equipes = json_data.get('equipes', [])
    # 1. Agréger les salaires par (poste, année)
    # Créer un dictionnaire pour regrouper par poste
    grouped = defaultdict(lambda: {"contract_type": None, "Annee 1": None, "Annee 2": None, "Annee 3": None})

    # Remplir le dictionnaire avec les données disponibles
    for item in equipes:
        post = item["post_name"].strip()
        grouped[post]["contract_type"] = item["contract_type"]
        
        # Gérer le cas où annee est une chaîne ou une liste
        if isinstance(item["annee"], list):
            # If it's a list, associate each year with its corresponding salary
            for i, annee in enumerate(item["annee"]):
                if isinstance(item["salaire_par_employe"], list) and i < len(item["salaire_par_employe"]):
                    salaire_str = str(item["salaire_par_employe"][i])
                    try:
                        salaire = float(salaire_str.replace(" ", "").replace(",", "."))
                        grouped[post][annee] = salaire
                    except:
                        pass
        else:
            # If it's a string, treat as before
            annee = item["annee"]
            salaire_str = str(item["salaire_par_employe"])
            try:
                salaire = float(salaire_str.replace(" ", "").replace(",", "."))
                grouped[post][annee] = salaire
            except:
                pass

    # 2. Préparer les données pour le graphique
    annees = ["Annee 1", "Annee 2", "Annee 3"]  # Années fixes pour assurer l'ordre
    plt.figure(figsize=(10, 6))

    # Tracer une ligne pour chaque poste
    for post, info in grouped.items():
        # Apply the same fallback logic as for the table
        annee1 = info["Annee 1"] or 0.0
        annee2 = info["Annee 2"] or annee1
        annee3 = info["Annee 3"] or annee2
        
        y_values = [annee1, annee2, annee3]
        plt.plot(annees, y_values, marker='o', label=post)

    # 3. Personnalisation
    plt.title("Charges salariales")
    plt.xlabel("Année")
    plt.ylabel("Salaires (DH)")
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.5)

    plt.tight_layout()
    # Sauvegarde de l'image
    plt.savefig("charges_salariales.png", bbox_inches='tight')
    plt.close()

    # Insertion dans le document Word
    image_path = "charges_salariales.png"
    context_tables["texte2_page21"] = InlineImage(doc, image_path, width=Cm(15))

    # Mise à jour du contexte
    context.update(context_tables)

    # Charges fixes Table
    subdoc_charges = doc.new_subdoc()
    data_charges = json_data.get('charges', [])

    # Create the table
    table_charges = subdoc_charges.add_table(rows=len(data_charges) + 1, cols=3)
    table_charges.style = "Table Grid"

    # Define column widths
    column_widths_charges = [Inches(3.0), Inches(1.5), Inches(2.0)]

    # Headers
    hdr_cells_charges = table_charges.rows[0].cells
    headers_charges = ['Type de charge', 'Fréquence', 'Montant TTC (Année 1)']
    for i, header in enumerate(headers_charges):
        hdr_cells_charges[i].text = header
        hdr_cells_charges[i].width = column_widths_charges[i]
        set_cell_background(hdr_cells_charges[i], "04A264")  # fond vert
        for p in hdr_cells_charges[i].paragraphs:
            p.alignment = 1  # Centrer le texte

    # Populate data rows
    for i, item in enumerate(data_charges, 1):
        row_cells = table_charges.rows[i].cells
        row_cells[0].text = item.get("typecharge", "")
        row_cells[1].text = item.get("frequence", "")
        
        # Format the montant_ttc with thousand separator and decimal comma
        montant = item.get("montant_ttc", 0)
        row_cells[2].text = f"{montant:,.2f}".replace(",", " ").replace(".", ",")

        for j in range(3):
            row_cells[j].width = column_widths_charges[j]
            for p in row_cells[j].paragraphs:
                p.alignment = 1  # Centrer le texte

    # Center the table
    tbl_charges = table_charges._tbl
    tblPr_charges = tbl_charges.tblPr
    if tblPr_charges is None:
        tblPr_charges = OxmlElement('w:tblPr')
        tbl_charges.insert(0, tblPr_charges)

    jc_charges = OxmlElement('w:jc')
    jc_charges.set(qn('w:val'), 'center')
    tblPr_charges.append(jc_charges)

    # Add to context
    context_tables["texte1_page22"] = subdoc_charges
    context.update(context_tables)

    # Charges fixes Table and Histogram
    subdoc_charges = doc.new_subdoc()
    data_charges = json_data.get('charges', [])

    # Create the table
    table_charges = subdoc_charges.add_table(rows=len(data_charges) + 1, cols=3)
    table_charges.style = "Table Grid"

    # Define column widths
    column_widths_charges = [Inches(3.0), Inches(1.5), Inches(2.0)]

    # Headers
    hdr_cells_charges = table_charges.rows[0].cells
    headers_charges = ['Type de charge', 'Fréquence', 'Montant TTC (Année 1)']
    for i, header in enumerate(headers_charges):
        hdr_cells_charges[i].text = header
        hdr_cells_charges[i].width = column_widths_charges[i]
        set_cell_background(hdr_cells_charges[i], "04A264")  # fond vert
        for p in hdr_cells_charges[i].paragraphs:
            p.alignment = 1  # Centrer le texte

    # Populate data rows
    for i, item in enumerate(data_charges, 1):
        row_cells = table_charges.rows[i].cells
        row_cells[0].text = item.get("typecharge", "")
        row_cells[1].text = item.get("frequence", "")
        
        # Format the montant_ttc with thousand separator and decimal comma
        montant = item.get("montant_ttc", 0)
        row_cells[2].text = f"{montant:,.2f}".replace(",", " ").replace(".", ",")

        for j in range(3):
            row_cells[j].width = column_widths_charges[j]
            for p in row_cells[j].paragraphs:
                p.alignment = 1  # Centrer le texte

    # Center the table
    tbl_charges = table_charges._tbl
    tblPr_charges = tbl_charges.tblPr
    if tblPr_charges is None:
        tblPr_charges = OxmlElement('w:tblPr')
        tbl_charges.insert(0, tblPr_charges)

    jc_charges = OxmlElement('w:jc')
    jc_charges.set(qn('w:val'), 'center')
    tblPr_charges.append(jc_charges)

    # Create histogram for charges
    plt.figure(figsize=(12, 6))

    # Extract data for the histogram
    types_charge = [item.get("typecharge", "") for item in data_charges]
    montants = [float(item.get("montant_ttc", 0)) for item in data_charges]

    # Sort data by montant for better visualization
    sorted_data = sorted(zip(types_charge, montants), key=lambda x: x[1], reverse=True)
    sorted_types = [item[0] for item in sorted_data]
    sorted_montants = [item[1] for item in sorted_data]

    # Create horizontal bar chart (easier to read with long labels)
    y_pos = np.arange(len(sorted_types))
    plt.barh(y_pos, sorted_montants, color='#4CAF50')
    plt.yticks(y_pos, sorted_types)
    plt.xlabel('Montant TTC (DH)')
    plt.title('Répartition des charges fixes')
    plt.grid(axis='x', linestyle='--', alpha=0.7)

    # Add values at the end of each bar
    for i, v in enumerate(sorted_montants):
        plt.text(v + 0.1, i, f"{v:,.2f}".replace(",", " ").replace(".", ","), 
                 va='center', fontsize=9)

    plt.tight_layout()
    plt.savefig("charges_histogram.png", bbox_inches='tight')
    plt.close()

    # Add both table and histogram to the document
    subdoc_charges.add_paragraph("")  # Add some space
    subdoc_charges.add_picture("charges_histogram.png", width=Cm(15))

    # Add to context
    context_tables["texte1_page22"] = subdoc_charges
    context.update(context_tables)

    # Conclusion
    # Check if data is a list or dictionary and handle accordingly
    if isinstance(json_data, list):
        # If it's a list, try to get the first item if it exists
        projet = json_data[0].get("name", "Nom inconnu") if json_data else "Nom inconnu"
    else:
        # If it's a dictionary, proceed as before
        projet = json_data.get("name", "Nom inconnu")

    data_objectifs = json_data['objectifs']
    # Similarly for description and detail_description_projet
    if isinstance(json_data, list):
        description = json_data[0].get("description", "Aucune description fournie.") if json_data else "Aucune description fournie."
        detail_description_projet = json_data[0].get("detail_description_projet", "Aucun détail fourni.") if json_data else "Aucun détail fourni."
    else:
        description = json_data.get("description", "Aucune description fournie.")
        detail_description_projet = json_data.get("detail_description_projet", "Aucun détail fourni.")

    # Prompt final
    prompt_conclusion = f"""
    Tu es un expert en rédaction de business plans.

    À partir des informations suivantes :
    {projet}
    {data_objectifs}
    {description}
    {detail_description_projet}

    Rédige une conclusion générale percutante et inspirante pour ce projet.
    Cette conclusion doit :
    - Résumer brièvement la problématique
    - Mettre en valeur la solution proposée et sa valeur ajoutée
    - Souligner les résultats attendus ou les objectifs visés
    - Être professionnelle, engageante et tournée vers l’avenir
    - Ne pas contenir de jargon technique
    - Faire maximum 150 mots
    """

    try:
        response_conclusion = model.generate_content(prompt_conclusion)
        texte_conclusion = response_conclusion.text.strip()
        print("✅ Conclusion générée avec succès !")
    except Exception as e:
        print(f"❌ Erreur lors de la génération de la conclusion : {str(e)}")
        texte_conclusion = "Conclusion du business plan non disponible pour le moment."

    context["texte1_page23"] = RichText(texte_conclusion)

    doc.render(context)
    doc.save(output_path)
