from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel
import tempfile
import os
import sys
import datetime
from src.azure_storage import AzureBlobStorage

from main import generate_ppt, generate_doc

app = FastAPI(
    title="Pitch Deck Generator API",
    description="API to generate pitch decks using different templates.",
    version="1.0.0"
)

# Allow CORS for all origins (customize as needed)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class GeneratePPTRequest(BaseModel):
    template_number: int
    data: dict

@app.post("/generate-ppt")
async def generate_ppt_endpoint(req: GeneratePPTRequest):
    template_number = req.template_number
    data = req.data

    if template_number not in [1, 2, 3]:
        raise HTTPException(status_code=400, detail="template_number must be 1, 2, or 3.")

    # Use the configured output directory from environment variable
    output_dir = os.getenv('GENERATED_DOCS_PATH', '0x012d16745cb81afb91d9f675450c1b5a/generated_docs')
    os.makedirs(output_dir, exist_ok=True)

    # Generate a unique filename with date and time format
    now = datetime.datetime.now()
    date_str = now.strftime("%Y-%m-%d")
    time_str = now.strftime("%H-%M-%S")
    filename = f"pitchdeck_{template_number}_{date_str}_{time_str}.pptx"
    output_path = os.path.join(output_dir, filename)

    try:
        # Generate PPT and get local path
        local_path = generate_ppt(template_number, data, output_path)
        
        # Upload to Azure Blob Storage
        """ azure_storage = AzureBlobStorage()
        blob_name = os.path.join(os.getenv('GENERATED_DOCS_PATH', ''), filename)
        blob_url = azure_storage.upload_file(local_path, blob_name) """
        
        return JSONResponse(
            content={
                "message": "Pitch deck generated successfully",
                "template_used": template_number,
                "local_path": local_path,
                #"azure_blob_url": blob_url
            }
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "error": str(e),
                "template_attempted": template_number
            }
        )

class GenerateDocRequest(BaseModel):
    data: dict

@app.post("/generate-doc")
async def generate_doc_endpoint(req: GenerateDocRequest):
    data = req.data

    # Use the configured output directory from environment variable
    output_dir = os.getenv('GENERATED_DOCS_PATH', '0x012d16745cb81afb91d9f675450c1b5a/generated_docs')
    doc_output_dir = os.path.join(output_dir, 'doc_output')
    os.makedirs(doc_output_dir, exist_ok=True)

    # Generate a unique filename with date and time format
    now = datetime.datetime.now()
    date_str = now.strftime("%Y-%m-%d")
    time_str = now.strftime("%H-%M-%S")
    filename = f"document_{date_str}_{time_str}.docx"
    output_path = os.path.join(doc_output_dir, filename)

    try:
        # Generate document and get local path
        local_path = generate_doc(data, output_path)
        
        # Upload to Azure Blob Storage
        """ azure_storage = AzureBlobStorage()
        blob_name = os.path.join('doc_output', filename)
        blob_url = azure_storage.upload_file(local_path, blob_name)  """        
        return JSONResponse(
            content={
                "message": "Document generated successfully",
                "local_path": local_path,
                #"azure_blob_url": blob_url
            }
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "error": str(e)
            }
        )
