from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
import json
from src.gemini_api import generate_content

def add_business_model_slide(prs, data):
    """
    Adds the Business Model Canvas slide to the presentation using Template 2 logic.

    This function generates Business Model Canvas (BMC) content using Google's Gemini AI,
    parses the AI's JSON response, and then populates the BMC slide in the PowerPoint
    presentation with the generated data. If the AI response is invalid or malformed,
    it falls back to default BMC data.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, used as input for AI content generation.
    """
    # Access the specific slide intended for the Business Model Canvas in Template 2.
    # In this template, it's assumed to be the 9th slide (index 8).
    slide_bmc = prs.slides[8]

    # Define the prompt for Gemini AI to generate Business Model Canvas content in JSON format.
    # The prompt instructs the AI to act as a business model expert and generate detailed,
    # relevant content for each BMC section, with a specific format and length constraint.
    prompt_bmc = (
        f"En tant qu'expert en business model, analyse le projet suivant : {data}\n"
        "Génère un Business Model Canvas détaillé et pertinent sous forme de JSON strict.\n"
        "Retourne UNIQUEMENT le JSON suivant rempli avec un contenu pertinent (3-4-5 éléments par section et 3-4 mots par élément) :\n"
        "{"
        '"partenaires_cles": [],'
        '"activites_cles": [],'
        '"ressources_cles": [],'
        '"proposition_valeur": [],'
        '"relation_client": [],'
        '"canaux": [],'
        '"segments_client": [],'
        '"structure_couts": [],'
        '"revenus": []'
        "}"
    )

    # --- Generate and Parse JSON Response from Gemini ---
    try:
        response_bmc = generate_content(prompt_bmc)
        json_str = response_bmc.strip()
        # Clean the JSON string by removing markdown code block delimiters if present.
        if json_str.startswith('```json'):
            json_str = json_str.split('```json')[1]
        if json_str.endswith('```'):
            json_str = json_str.split('```')[0]
        json_str = json_str.strip()
        # Load the cleaned JSON string into a Python dictionary.
        bmc_data = json.loads(json_str)
    except Exception:
        # If parsing fails or AI response is problematic, use default BMC data.
        # This ensures the slide can still be generated.
        bmc_data = {
            "partenaires_cles": ["Fournisseurs clés", "Partenaires stratégiques", "Distributeurs"],
            "activites_cles": ["Développement", "Marketing", "Support client"],
            "ressources_cles": ["Équipe technique", "Infrastructure", "Propriété intellectuelle"],
            "proposition_valeur": ["Innovation", "Qualité", "Service client"],
            "relation_client": ["Support dédié", "Communauté", "Formation"],
            "canaux": ["Web", "Mobile", "Partenaires"],
            "segments_client": ["Entreprises", "Particuliers", "Institutions"],
            "structure_couts": ["R&D", "Marketing", "Opérations"],
            "revenus": ["Abonnements", "Services", "Licences"]
        }

    # Helper function to add text content to specific sections of the BMC slide.
    def add_bmc_text(slide, text_list, left, top, width=Inches(2), height=Inches(2)):
        """
        Adds a list of text items to a textbox on the slide at specified coordinates.

        Args:
            slide (pptx.slide.Slide): The slide object to which the text will be added.
            text_list (list): A list of strings, where each string represents an item
                              to be added as a bullet point.
            left (float): The x-coordinate (in inches) for the left edge of the textbox.
            top (float): The y-coordinate (in inches) for the top edge of the textbox.
            width (pptx.util.Inches): The width of the textbox.
            height (pptx.util.Inches): The height of the textbox.
        """
        # Add a new textbox shape to the slide at the specified position and size.
        textbox = slide.shapes.add_textbox(Inches(left), Inches(top), width, height)
        # Get the text frame of the newly added textbox.
        text_frame = textbox.text_frame
        text_frame.clear() # Clear any existing text in the text frame.
        # Iterate through the list of text items to add them as bullet points.
        for i, item in enumerate(text_list):
            if i == 0:
                # For the first item, use the default first paragraph.
                paragraph = text_frame.paragraphs[0]
            else:
                # For subsequent items, add a new paragraph.
                paragraph = text_frame.add_paragraph()
            # Set the text of the paragraph, prepending a bullet point.
            paragraph.text = "• " + item
            # Apply consistent font formatting to all runs within the paragraph.
            for run in paragraph.runs:
                run.font.size = Pt(20)  # Set font size to 20 points.
                run.font.color.rgb = RGBColor(0, 0, 0) # Set font color to black.
                run.font.name = "Calibri" # Set font name to Calibri.

    # --- Add Content to Each Section of the Business Model Canvas ---
    # Each call specifies the slide, the relevant data list from bmc_data (with a default empty list),
    # and the x, y coordinates for positioning the text box on the slide.
    add_bmc_text(slide_bmc, bmc_data.get("partenaires_cles", []), 0, 1.5)
    add_bmc_text(slide_bmc, bmc_data.get("activites_cles", []), 4, 1.5)
    add_bmc_text(slide_bmc, bmc_data.get("ressources_cles", []), 4, 4.9)
    add_bmc_text(slide_bmc, bmc_data.get("proposition_valeur", []), 8, 1.5)
    add_bmc_text(slide_bmc, bmc_data.get("relation_client", []), 12.1, 1.5)
    add_bmc_text(slide_bmc, bmc_data.get("canaux", []), 12.1, 4.9)
    add_bmc_text(slide_bmc, bmc_data.get("segments_client", []), 16.2, 1.5)
    add_bmc_text(slide_bmc, bmc_data.get("structure_couts", []), 0, 8, width=Inches(5), height=Inches(1.5))
    add_bmc_text(slide_bmc, bmc_data.get("revenus", []), 10.5, 8, width=Inches(5), height=Inches(1.5))
