"""
Product slide module.
Responsible for rendering the products/services section of the pitch deck.
"""

"""
Product slide module.
Responsible for rendering the products/services section of the pitch deck.
"""

from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from pptx.enum.shapes import MSO_SHAPE

def add_product_slide(prs, data):
    """
    Adds the products/services content to the appropriate slide(s) in the presentation.

    This function dynamically creates slides for products/services offered by the project.
    If no offers are provided in the `data`, it adds a placeholder message. Otherwise,
    it calculates the number of slides needed based on a card layout, adds new slides
    if necessary, and then populates each slide with product/service cards, including
    details like name, description, and price, with specific styling.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, containing 'offers' (products/services) information.
    """
    # Retrieve offers data from the 'data' dictionary, defaulting to an empty list.
    offers = data.get("offers", [])

    # If no offers are defined, add a placeholder message to the 11th slide (index 10) and return.
    if not offers:
        slide = prs.slides[10]
        text_box = slide.shapes.add_textbox(Inches(2), <PERSON><PERSON>(3), <PERSON><PERSON>(6), Inches(1))
        text_frame = text_box.text_frame
        text_frame.text = "Aucun produit ou service défini pour ce projet"
        for paragraph in text_frame.paragraphs:
            paragraph.alignment = PP_ALIGN.CENTER
            for run in paragraph.runs:
                run.font.size = Pt(18)
                run.font.color.rgb = RGBColor(255, 255, 255)
        return

    # --- Card Layout Parameters ---
    # Define dimensions and spacing for product/service cards.
    box_width = Inches(2.2)
    box_height = Inches(1.5)
    h_spacing = Inches(0.2) # Horizontal spacing between cards.
    v_spacing = Inches(0.1) # Vertical spacing between cards.
    start_left = Inches(1.2)
    start_top = Inches(1.3)
    products_per_row = 3
    rows_per_slide = 2
    products_per_slide = products_per_row * rows_per_slide
    # Calculate the number of slides required based on the number of offers.
    num_slides_needed = (len(offers) + products_per_slide - 1) // products_per_slide

    # Access the initial product slide (11th slide, index 10).
    slide11 = prs.slides[10]
    additional_slides = []
    # If more than one slide is needed, add new slides and reorder them.
    if num_slides_needed > 1:
        insert_position = 11 # Position to insert new slides.
        for i in range(1, num_slides_needed):
            slide_layout = slide11.slide_layout # Use the same layout as the initial slide.
            new_slide = prs.slides.add_slide(slide_layout) # Add a new slide.
            additional_slides.append(new_slide) # Store reference to the new slide.
            # Reorder slides to insert the new slide at the correct position.
            slides = list(prs.slides._sldIdLst)
            new_order = slides[:insert_position] + [slides[-1]] + slides[insert_position:-1]
            prs.slides._sldIdLst[:] = new_order
            insert_position += 1 # Increment insert position for the next slide.

    # --- Populate Slides with Product/Service Cards ---
    # Iterate through each required slide.
    for slide_index in range(num_slides_needed):
        if slide_index == 0:
            current_slide = slide11 # Use the initial slide for the first set of offers.
        else:
            current_slide = additional_slides[slide_index - 1] # Use additional slides for subsequent sets.
        
        # Determine the range of offers for the current slide.
        start_index = slide_index * products_per_slide
        end_index = min(start_index + products_per_slide, len(offers))
        current_offers = offers[start_index:end_index]

        # Iterate through the offers for the current slide to create and style cards.
        for i, offer in enumerate(current_offers):
            local_index = i
            row = local_index // products_per_row # Calculate row for card placement.
            col = local_index % products_per_row  # Calculate column for card placement.
            # Calculate the exact position (left, top) for the current card.
            left = start_left + (col * (box_width + h_spacing))
            top = start_top + (row * (box_height + v_spacing))
            
            # Add a rounded rectangle shape for the product/service card.
            rectangle = current_slide.shapes.add_shape(
                MSO_SHAPE.ROUNDED_RECTANGLE,
                left, top, box_width, box_height
            )
            # Apply styling to the card: solid dark gray fill, white border.
            rectangle.fill.solid()
            rectangle.fill.fore_color.rgb = RGBColor(40, 40, 40) # Dark gray background.
            rectangle.line.color.rgb = RGBColor(255, 255, 255) # White border.
            rectangle.line.width = Pt(1.5)
            
            # Add a textbox for the content within the card.
            text_box = current_slide.shapes.add_textbox(
                left + Inches(0),
                top + Inches(0.1),
                box_width,
                box_height
            )
            text_frame = text_box.text_frame
            text_frame.word_wrap = True # Enable word wrapping.

            # Extract offer details, providing defaults if missing.
            name = offer.get("name", "Nom non disponible")
            description = offer.get("description", "Description non disponible")
            price = offer.get("prix_vente_ttc", 0)

            # Add product/service name.
            p1 = text_frame.paragraphs[0]
            p1.text = name
            p1.font.size = Pt(8)    # Set font size.
            p1.font.bold = True     # Set font to bold.
            p1.font.color.rgb = RGBColor(255, 255, 255) # White text.
            p1.alignment = PP_ALIGN.CENTER # Center align.
            
            # Add a blank paragraph for spacing.
            p_space = text_frame.add_paragraph()
            p_space.text = ""
            
            # Add product/service description.
            p2 = text_frame.add_paragraph()
            p2.text = description
            p2.font.size = Pt(7)    # Set font size.
            p2.font.color.rgb = RGBColor(200, 200, 200) # Light gray text.
            p2.alignment = PP_ALIGN.CENTER # Center align.
            
            # Add another blank paragraph for spacing.
            p_space = text_frame.add_paragraph()
            p_space.text = ""
            
            # Add formatted price.
            p3 = text_frame.add_paragraph()
            p3.text = f"Prix: {price:,.2f} DH"
            p3.font.size = Pt(8)    # Set font size.
            p3.font.bold = True     # Set font to bold.
            p3.font.color.rgb = RGBColor(255, 255, 255) # White text.
            p3.alignment = PP_ALIGN.CENTER # Center align.
