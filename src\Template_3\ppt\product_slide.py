"""
Product slide module.
Handles the creation and formatting of the product/service slide.
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN

def create_product_slide(prs, data):
    """
    Creates the product/service slide.
    
    Args:
        prs: PowerPoint presentation object
        data: Dictionary containing project data
    """
    # Access the seventh slide (index 6)
    slide = prs.slides[6]
    
    # Get offers from data
    offers = data.get("offers", [])
    
    if offers:
        # Add text box for product list
        textbox = slide.shapes.add_textbox(Inches(1), Inches(1), Inches(10), Inches(5.5))
        text_frame = textbox.text_frame
        text_frame.word_wrap = True
        
        # Add each product to the slide
        for offer in offers:
            name = offer.get("name", "Produit sans nom")
            prix = f"{offer.get('prix_vente_ttc', 0):,.2f} DH"
            sentence = f"- {name} : {prix}."
            
            p = text_frame.add_paragraph()
            p.text = sentence
            p.alignment = PP_ALIGN.LEFT
            run = p.runs[0]
            run.font.size = Pt(14)
            run.font.color.rgb = RGBColor(0, 0, 0)
    else:
        # Add placeholder text if no products found
        textbox = slide.shapes.add_textbox(Inches(1), Inches(1), Inches(7), Inches(5.5))
        text_frame = textbox.text_frame
        text_frame.text = "Aucun produit ou service trouvé dans les données."
