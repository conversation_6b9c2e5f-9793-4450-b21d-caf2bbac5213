from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from src.gemini_api import generate_content
from src.Template_2.text_utils import wrap_text

def add_introduction_slide(prs, data):
    """
    Adds the 'about/introduction' slide to the presentation using Template 2 logic.

    This function generates an introductory description of the project using
    Google's Gemini AI, focusing on market opportunity or emerging needs.
    The generated text is then added to the introduction slide in the PowerPoint
    presentation, with specific styling.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, used as input for AI content generation.
    """
    # Access the specific slide intended for the introduction in Template 2.
    # In this template, it's assumed to be the 3rd slide (index 2).
    slide = prs.slides[2]

    # --- Generate Introduction Text with Gemini AI ---
    # Define the prompt for Gemini AI to generate a project description.
    # The prompt specifies focusing on market opportunity, a professional tone,
    # and a structured output (3-4 sentences, max 10 lines).
    prompt_about = (
        f"Présente une petite description  du projet '{data}' "
        "en mettant en avant l’opportunité du marché ou les besoins émergents, "
        "sans encore évoquer le problème ou la solution. "
        "Le ton doit être professionnel, crédible et axé impact. "
        "Rédige 3-4 phrases informatives et bien structurées en 10lignes maximum."
    )

    # Generate content using the Gemini API.
    about_text_raw = generate_content(prompt_about)
    # Wrap the generated text to ensure it fits within the slide's layout, with a line limit of 9.
    about_text = wrap_text(about_text_raw, 9)

    # --- Add Introduction Content to the Slide ---
    # Define the position and size for the text box.
    left_text_about = Inches(1.5)
    top_text_about = Inches(2.5)
    width_text_about = Inches(6)
    height_text_about = Inches(3)

    # Add a textbox shape to the slide at the specified position and size.
    text_box_about = slide.shapes.add_textbox(left_text_about, top_text_about, width_text_about, height_text_about)
    # Get the text frame of the textbox.
    text_frame_about = text_box_about.text_frame
    # Set the text content.
    text_frame_about.text = about_text

    # Apply font formatting to all paragraphs and runs within the text frame.
    for paragraph in text_frame_about.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(35)  # Set font size.
            run.font.bold = False   # Set font to not bold.
            run.font.color.rgb = RGBColor(0, 0, 0) # Set font color to black.
