from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from src.gemini_api import generate_content
from src.Template_2.text_utils import wrap_text

def add_solution_slide(prs, data):
    """
    Adds the 'solution' slide to the presentation using Template 2 logic.

    This function generates a concise and impactful solution description for the project
    using Google's Gemini AI, focusing on innovative approaches and benefits.
    The generated text is then added to the solution slide in the PowerPoint
    presentation, with specific styling and justification.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, used as input for AI content generation.
    """
    # Access the specific slide intended for the solution in Template 2.
    # In this template, it's assumed to be the 5th slide (index 4).
    slide = prs.slides[4]

    # --- Generate Solution Text with Gemini AI ---
    # Define the prompt for Gemini AI to generate a succinct and impactful solution description.
    # The prompt specifies a 3-sentence structure, focusing on innovative approaches and benefits.
    prompt_solution = (
        f"En tant qu'expert dans la rédaction de pitch decks, Rédige une solution concise et percutante pour le projet '{data}', "
        "en exposant clairement les approches innovantes et les bénéfices de cette solution, sans entrer dans les détails techniques. "
        "Structure le texte en 3 phrases courtes, en utilisant des retours à la ligne pour la lisibilité."
    )

    # Generate content using the Gemini API.
    solution_text_raw = generate_content(prompt_solution)
    # Wrap the generated text to ensure it fits within the slide's layout, with a line limit of 7.
    solution_text = wrap_text(solution_text_raw, 7)

    # --- Add Solution Content to the Slide ---
    # Define the position and size for the text box.
    left_content = Inches(7.6)
    top_content = Inches(3)
    width_content = Inches(6)
    height_content = Inches(3)

    # Add a textbox shape to the slide at the specified position and size.
    text_box_content = slide.shapes.add_textbox(left_content, top_content, width_content, height_content)
    # Get the text frame of the textbox.
    text_frame_content = text_box_content.text_frame
    # Set the text content.
    text_frame_content.text = solution_text

    # Apply font formatting and alignment to all paragraphs and runs within the text frame.
    for paragraph in text_frame_content.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(35)  # Set font size.
            run.font.bold = False   # Set font to not bold.
            run.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.
        paragraph.alignment = PP_ALIGN.JUSTIFY # Justify align the text.
