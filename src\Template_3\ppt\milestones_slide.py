"""
Milestones slide module.
Handles the creation and formatting of the project milestones slide.
"""

from datetime import datetime
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN

def create_milestones_slide(prs, data):
    """
    Creates the milestones slide.
    
    Args:
        prs: PowerPoint presentation object
        data: Dictionary containing project data
    """
    # Access the thirteenth slide (index 12)
    slide = prs.slides[12]
    
    # Get milestones from data
    jalons = data.get("jalons", [])
    
    # Calculate number of slides needed (6 rows per slide)
    rows_per_slide = 6
    num_slides_needed = (len(jalons) + rows_per_slide - 1) // rows_per_slide
    
    # Process each slide
    for slide_index in range(num_slides_needed):
        if slide_index == 0:
            current_slide = slide
        else:
            # Create additional slide if needed
            slide_layout = slide.slide_layout
            new_slide = prs.slides.add_slide(slide_layout)
            prs.slides._sldIdLst.insert(12 + slide_index, prs.slides._sldIdLst[-1])
            current_slide = prs.slides[12 + slide_index]
        
        # Determine milestones for this slide
        start_index = slide_index * rows_per_slide
        end_index = min(start_index + rows_per_slide, len(jalons))
        current_jalons = jalons[start_index:end_index]
        
        # Add table to slide
        table_top = Inches(1.2)
        table_left = Inches(1)
        table_width = Inches(8)
        table_height = Inches(0.5) * (len(current_jalons) + 1)
        
        table = current_slide.shapes.add_table(
            rows=len(current_jalons) + 1,
            cols=5,
            left=table_left,
            top=table_top,
            width=table_width,
            height=table_height
        ).table
        
        # Fill table header
        headers = ["Jalon", "Date de début", "Période", "Montant (DH)", "Utilisation"]
        for col_index, header in enumerate(headers):
            cell = table.cell(0, col_index)
            cell.text = header
            cell.fill.solid()
            cell.fill.fore_color.rgb = RGBColor(0, 102, 255)
            for paragraph in cell.text_frame.paragraphs:
                paragraph.alignment = PP_ALIGN.CENTER
                for run in paragraph.runs:
                    run.font.size = Pt(11)
                    run.font.bold = True
                    run.font.color.rgb = RGBColor(255, 255, 255)

        # Fill table data
        for row_index, jalon in enumerate(current_jalons, start=1):
            num_jalon = row_index + (slide_index * rows_per_slide)
            
            # Format date
            date_debut_str = jalon.get('date_delivrable', '')
            if date_debut_str:
                try:
                    date_obj = datetime.strptime(date_debut_str.split('T')[0], "%Y-%m-%d")
                    date_debut_formatted = date_obj.strftime("%d/%m/%Y")
                except:
                    date_debut_formatted = 'Non spécifiée'
            else:
                date_debut_formatted = 'Non spécifiée'
            
            # Format other fields
            periode = f"{jalon['term']} Jours" if jalon['term'] else "T0 (lancement)"
            nom_etape = jalon['name'] if jalon['name'] else jalon['description']
            montant = f"{jalon['amount']:,.2f} DH" if jalon['amount'] else "Non spécifié"
            utilisation = jalon['description'] if jalon['description'] else "Non spécifié"
            
            # Insert data into cells
            table.cell(row_index, 0).text = str(num_jalon)
            table.cell(row_index, 1).text = date_debut_formatted
            table.cell(row_index, 2).text = periode
            table.cell(row_index, 3).text = montant
            table.cell(row_index, 4).text = utilisation
            
            # Format cells
            for col_index in range(5):
                cell = table.cell(row_index, col_index)
                cell.fill.solid()
                cell.fill.fore_color.rgb = RGBColor(240, 240, 240)
                for paragraph in cell.text_frame.paragraphs:
                    paragraph.alignment = PP_ALIGN.LEFT
                    for run in paragraph.runs:
                        run.font.size = Pt(10)
                        run.font.color.rgb = RGBColor(0, 0, 0)
        
        # Set column widths
        column_widths = [Inches(0.5), Inches(1.5), Inches(1.2), Inches(1.8), Inches(3)]
        for col_index, width in enumerate(column_widths):
            table.columns[col_index].width = width
