from azure.storage.blob import BlobServiceClient
import os
from datetime import datetime

class AzureBlobStorage:
    def __init__(self):
        connection_string = os.getenv('AZURE_STORAGE_CONNECTION_STRING')
        if not connection_string:
            raise ValueError("AZURE_STORAGE_CONNECTION_STRING environment variable is not set")
        
        self.blob_service_client = BlobServiceClient.from_connection_string(connection_string)
        self.container_name = os.getenv('AZURE_STORAGE_CONTAINER_NAME', 'pitchdecks')

    def upload_file(self, file_path, blob_name=None):
        """
        Upload a file to Azure Blob Storage
        :param file_path: Local path of the file to upload
        :param blob_name: Name to give the blob (optional). If not provided, will use filename with timestamp
        :return: URL of the uploaded blob
        """
        if not blob_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            blob_name = f"{os.path.basename(file_path)}_{timestamp}"

        container_client = self.blob_service_client.get_container_client(self.container_name)
        
        # Create container if it doesn't exist
        try:
            container_client.create_container()
        except Exception as e:
            print(f"Container might already exist: {e}")

        blob_client = container_client.get_blob_client(blob_name)

        with open(file_path, "rb") as data:
            blob_client.upload_blob(data, overwrite=True)

        return blob_client.url 