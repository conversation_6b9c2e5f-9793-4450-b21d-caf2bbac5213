from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor

def add_title_slide(prs, data):
    """
    Adds the title/contact slide to the presentation using Template 2 logic.

    This function populates the first slide of the presentation with the project
    name as the main title and contact information (phone, email, website)
    in a footer-like section at the bottom of the slide.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, containing project 'name'
                     and contact information.
    """
    # Access the first slide of the presentation (index 0).
    slide = prs.slides[0]

    # --- Layout Parameters for Main Title ---
    left_text = Inches(2)
    top_text = Inches(5.5)
    width_text = Inches(3.5)
    height_text = Inches(2)

    # Retrieve project name and contact information from the 'data' dictionary.
    nom_projet = data.get("name", "Projet Inconnu")
    phone_number = data.get("phone")
    email = data.get("email")
    website = data.get("website_url")

    # --- Add Project Name as Title ---
    # Add a textbox shape for the project title.
    text_box = slide.shapes.add_textbox(left_text, top_text, width_text, height_text)
    text_frame = text_box.text_frame
    # Set the text content to "Présentation du projet [Project Name]".
    text_frame.text = f"Présentation du projet {nom_projet}"

    # Apply font formatting to the project title.
    for paragraph in text_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(40)  # Set font size.
            run.font.bold = True    # Set font to bold.
            run.font.color.rgb = RGBColor(0, 0, 0) # Set font color to black.

    # --- Layout Parameters for Footer Contact Information ---
    left_text_phone = Inches(1.0)   # Left position for phone number.
    left_text_address = Inches(4.5) # Left position for email.
    left_text_website = Inches(8.0) # Left position for website.
    top_text_footer = Inches(9.5)   # Top position for all footer text boxes.
    width_text_footer = Inches(2.5) # Width of each footer text box.
    height_text_footer = Inches(0.5) # Height of each footer text box.

    # --- Add Phone Number to Footer ---
    text_box_phone = slide.shapes.add_textbox(left_text_phone, top_text_footer, width_text_footer, height_text_footer)
    text_frame_phone = text_box_phone.text_frame
    text_frame_phone.text = f"Téléphone: {phone_number}"

    # --- Add Email to Footer ---
    text_box_address = slide.shapes.add_textbox(left_text_address, top_text_footer, width_text_footer, height_text_footer)
    text_frame_address = text_box_address.text_frame
    text_frame_address.text = f"E-mail: {email}"

    # --- Add Website to Footer ---
    text_box_website = slide.shapes.add_textbox(left_text_website, top_text_footer, width_text_footer, height_text_footer)
    text_frame_website = text_box_website.text_frame
    text_frame_website.text = f"Site: {website}"

    # --- Format Text for All Footer Sections ---
    # Iterate through each text frame and apply consistent font styling.
    for tf in [text_frame_phone, text_frame_address, text_frame_website]:
        for paragraph in tf.paragraphs:
            for run in paragraph.runs:
                run.font.size = Pt(16)  # Set font size.
                run.font.bold = True    # Set font to bold.
                run.font.color.rgb = RGBColor(0, 0, 0) # Set font color to black.
