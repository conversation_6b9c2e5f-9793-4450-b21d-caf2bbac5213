"""
PowerPoint helpers module.
Contains reusable functions for PowerPoint operations such as adding styled textboxes, tables, and other slide elements.
"""

from pptx.util import Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN

def add_styled_textbox(slide, left, top, width, height, text, font_size=20, bold=False, color=(255,255,255), alignment=PP_ALIGN.LEFT):
    """
    Adds a styled textbox to a slide.
    Args:
        slide: The pptx slide object.
        left, top, width, height: Position and size (pptx.util.Inches or EMU).
        text (str): The text to display.
        font_size (int): Font size in points.
        bold (bool): Whether the text is bold.
        color (tuple): RGB color tuple.
        alignment: pptx.enum.text.PP_ALIGN value.
    Returns:
        The created textbox shape.
    """
    text_box = slide.shapes.add_textbox(left, top, width, height)
    text_frame = text_box.text_frame
    text_frame.text = text
    for paragraph in text_frame.paragraphs:
        paragraph.alignment = alignment
        for run in paragraph.runs:
            run.font.size = Pt(font_size)
            run.font.bold = bold
            run.font.color.rgb = RGBColor(*color)
    return text_box

def add_styled_table(slide, rows, cols, left, top, width, height, header_fill=(0,0,0), cell_fill=(0,0,0), font_color=(255,255,255), font_size=16):
    """
    Adds a styled table to a slide.
    Args:
        slide: The pptx slide object.
        rows, cols (int): Table dimensions.
        left, top, width, height: Position and size (pptx.util.Inches or EMU).
        header_fill, cell_fill: RGB color tuples for header and cells.
        font_color: RGB color tuple for text.
        font_size: Font size in points.
    Returns:
        The created table shape.
    """
    table_shape = slide.shapes.add_table(rows, cols, left, top, width, height)
    table = table_shape.table
    for row_idx, row in enumerate(table.rows):
        for cell in row.cells:
            cell.fill.solid()
            cell.fill.fore_color.rgb = RGBColor(*(header_fill if row_idx == 0 else cell_fill))
            for paragraph in cell.text_frame.paragraphs:
                for run in paragraph.runs:
                    run.font.size = Pt(font_size)
                    run.font.color.rgb = RGBColor(*font_color)
    return table
