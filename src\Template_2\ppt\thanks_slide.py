from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor

def add_thanks_slide(prs, data):
    """
    Adds the thanks/contact slide to the presentation using Template 2 logic.

    This function populates the final slide of the presentation with contact
    information (phone, email, website) retrieved from the `data` dictionary.
    It positions these details in a footer-like section at the bottom of the slide.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, containing contact information.
    """
    # Access the specific slide intended for the thanks/contact information in Template 2.
    # In this template, it's assumed to be the 15th slide (index 14).
    slide = prs.slides[14]

    # Retrieve contact information from the 'data' dictionary.
    phone_number = data.get("phone")
    email = data.get("email")
    website = data.get("website_url")

    # --- Layout Parameters for Footer Text Boxes ---
    left_text_phone = Inches(1.0)   # Left position for phone number.
    left_text_address = Inches(4.5) # Left position for email.
    left_text_website = Inches(8.0) # Left position for website.
    top_text_footer = Inches(9.5)   # Top position for all footer text boxes.
    width_text = Inches(2.5)        # Width of each footer text box.
    height_text = Inches(0.5)       # Height of each footer text box.

    # --- Add Phone Number Text Box ---
    text_box_phone = slide.shapes.add_textbox(left_text_phone, top_text_footer, width_text, height_text)
    text_frame_phone = text_box_phone.text_frame
    text_frame_phone.text = f"Téléphone: {phone_number}"

    # --- Add Email Text Box ---
    text_box_address = slide.shapes.add_textbox(left_text_address, top_text_footer, width_text, height_text)
    text_frame_address = text_box_address.text_frame
    text_frame_address.text = f"E-mail: {email}"

    # --- Add Website Text Box ---
    text_box_website = slide.shapes.add_textbox(left_text_website, top_text_footer, width_text, height_text)
    text_frame_website = text_box_website.text_frame
    text_frame_website.text = f"Site: {website}"

    # --- Format Text for All Footer Sections ---
    # Iterate through each text frame and apply consistent font styling.
    for tf in [text_frame_phone, text_frame_address, text_frame_website]:
        for paragraph in tf.paragraphs:
            for run in paragraph.runs:
                run.font.size = Pt(16)  # Set font size.
                run.font.bold = True    # Set font to bold.
                run.font.color.rgb = RGBColor(0, 0, 0) # Set font color to black.
