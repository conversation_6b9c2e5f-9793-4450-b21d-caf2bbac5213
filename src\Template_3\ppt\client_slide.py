"""
Client slide module.
Handles the creation and formatting of the client target slide.
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.enum.shapes import MSO_SHAPE
from src.gemini_api import generate_content

def create_client_slide(prs, data):
    print("create_client_slide function is being executed")
    try:
        """
        Creates the client target slide.
        
        Args:
            prs: PowerPoint presentation object
            data: Dictionary containing project data
        """
        # Generate client target text using Gemini
        prompt_cible = (
            "Tu es expert en rédaction de pitch decks. "
            f"À partir des informations suivantes : {data}, identifie UNIQUEMENT les segments de clients cibles. "
            "- Liste uniquement les types de clients visés . "
            "- Pas de phrases, juste des catégories de clients claires et précises. "
            "- Maximum 6-9 mots par point. "
            "- Pas d'informations sur le produit, seulement les clients."
        )
        client_target_text = generate_content(prompt_cible).strip() or "Client cible non défini."
        
        # Access the sixth slide (index 5)
        slide = prs.slides[5]
        
       

        # Add text box with client target text
        left_content = Inches(1)
        top_content = Inches(2)
        width_content = Inches(6)
        height_content = Inches(4)
        
        text_box = slide.shapes.add_textbox(left_content, top_content, width_content, height_content)
        text_frame = text_box.text_frame
        text_frame.text = client_target_text
        
        # Format text
        for paragraph in text_frame.paragraphs:
            for run in paragraph.runs:
                run.font.size = Pt(16)
                run.font.bold = False
                run.font.color.rgb = RGBColor(0, 0, 0)
                
        # Recommended segments
        prompt_clients_recommandes = (
            f"En tant qu'expert, en utilisant les informations suivantes sur les clients cibles existants : {data}, "
            "propose d'autres clients que le porteur du projet pourrait cibler. Ces segments ne doivent pas inclure les clients déjà mentionnés. "
            "Sois créatif et cible des groupes qui pourraient être intéressés par ce projet, mais qui n'ont pas encore été considérés. "
            "Donne ta réponse sous forme de point bref sans explication, en mettant en avant 3 segments de clients . "
            "Format exact à suivre:\nPremier client ,\nDeuxième client,\nTroisième client"
        )
        clients_recommandes_text = generate_content(prompt_clients_recommandes).strip()
        segments = [seg.strip() for seg in clients_recommandes_text.split(",") if seg.strip()]
        segments = segments[:3] # Ensure only 3 segments max

        # --- Recommended segments styling ---
        box_count = 3
        box_width = Inches(2.0)  # Reduced width
        box_height = Inches(0.7) # Reduced height
        gap = Inches(0.2)      # Reduced gap
        total_width = box_count * box_width + (box_count - 1) * gap
        left_start = (prs.slide_width - total_width) / 2
        top_pos_box = prs.slide_height - box_height - Inches(0.3) # Position closer to bottom

        # Title above boxes
        title_text = "AKKAN recommande aussi :"
        title_height = Inches(0.3) # Reduced height
        title_box = slide.shapes.add_textbox(left_start, top_pos_box - title_height - Inches(0.1), total_width, title_height) # Reduced gap above title
        title_frame = title_box.text_frame
        title_frame.clear()
        p_title = title_frame.paragraphs[0]
        p_title.text = title_text
        p_title.alignment = PP_ALIGN.CENTER
        for run in p_title.runs:
            run.font.size = Pt(16) # Reduced font size
            run.font.bold = True
            run.font.color.rgb = RGBColor(0, 0, 0) # Black Title

        # Segment boxes (3 horizontally, styled)
        for i, seg in enumerate(segments):
            left = left_start + i * (box_width + gap)
            shape = slide.shapes.add_shape(
                MSO_SHAPE.ROUNDED_RECTANGLE,
                left,
                top_pos_box,
                box_width,
                box_height
            )
            # Style: light gray background, dark gray border, slight shadow
            shape.fill.solid()
            shape.fill.fore_color.rgb = RGBColor(220, 220, 220) # Light Gray Background
            shape.line.color.rgb = RGBColor(100, 100, 100) # Dark Gray Border
            shape.line.width = Pt(2)
            shape.shadow.inherit = False
            shape.shadow.blur_radius = Pt(6)
            shape.shadow.distance = Pt(1.5)

            # Centered text, black, not bold, smaller margins
            text_frame = shape.text_frame
            text_frame.clear()
            text_frame.word_wrap = True
            text_frame.vertical_anchor = MSO_ANCHOR.MIDDLE
            text_frame.margin_left = Inches(0.1)  # Reduced margin
            text_frame.margin_right = Inches(0.1) # Reduced margin
            text_frame.margin_top = Inches(0.05) # Reduced margin
            text_frame.margin_bottom = Inches(0.05)# Reduced margin

            p = text_frame.paragraphs[0]
            p.text = seg
            p.alignment = PP_ALIGN.CENTER
            for run in p.runs:
                run.font.size = Pt(10) # Reduced font size
                run.font.bold = False
                run.font.color.rgb = RGBColor(0, 0, 0) # Black Text
    except Exception as e:
        print(f"Error in create_client_slide: {e}")
