from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
import json
from pptx.enum.text import PP_ALIGN
from src.gemini_api import generate_content

def add_swot_slide(prs, data):
    """
    Adds the SWOT analysis slide to the presentation using Template 2 logic.

    This function generates a concise SWOT (Strengths, Weaknesses, Opportunities, Threats)
    analysis using Google's Gemini AI, parses the AI's JSON response, and then populates
    the SWOT slide in the PowerPoint presentation with the generated data. It displays
    each SWOT category with its respective points in a structured layout.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, used as input for AI content generation.
    """
    # Access the specific slide intended for the SWOT analysis in Template 2.
    # In this template, it's assumed to be the 11th slide (index 10).
    slide = prs.slides[10]

    # --- Generate SWOT Analysis with Gemini AI ---
    # Define the prompt for Gemini AI to generate a concise SWOT analysis in JSON format.
    # The prompt specifies strict JSON output, maximum word count per point, and directness.
    prompt_swot = (
        f"Analyse le projet suivant et génère une analyse SWOT très concise: {data}\n"
        "IMPORTANT: \n"
        "1. Ta réponse doit être UNIQUEMENT un objet JSON valide\n"
        "2. Chaque point DOIT faire maximum 5-6 mots\n"
        "3. Sois direct et précis\n"
        "4. Pas de phrases, juste des points clés\n"
        "Format exact à suivre:\n"
        "{\n"
        '    "forces": [\n'
        '        "Premier point fort du projet",\n'
        '        "Deuxième point fort du projet",\n'
        '        "Troisième point fort du projet"\n'
        "    ],\n"
        '    "faiblesses": [\n'
        '        "Première faiblesse du projet",\n'
        '        "Deuxième faiblesse du projet",\n'
        '        "Troisième faiblesse du projet"\n'
        "    ],\n"
        '    "opportunites": [\n'
        '        "Première opportunité pour le projet",\n'
        '        "Deuxième opportunité pour le projet",\n'
        '        "Troisième opportunité pour le projet"\n'
        "    ],\n"
        '    "menaces": [\n'
        '        "Première menace pour le projet",\n'
        '        "Deuxième menace pour le projet",\n'
        '        "Troisième menace pour le projet"\n'
        "    ]\n"
        "}"
    )

    # Generate content using the Gemini API.
    response_swot = generate_content(prompt_swot)
    # Strip any leading/trailing whitespace from the raw AI response.
    swot_text = response_swot.strip()

    # --- Parse JSON Response ---
    try:
        # Find the start and end of the JSON object within the AI's response.
        json_start = swot_text.find('{')
        json_end = swot_text.rfind('}') + 1
        if json_start != -1 and json_end != -1:
            # Extract the clean JSON string.
            clean_json = swot_text[json_start:json_end]
            # Load the JSON string into a Python dictionary.
            swot_data = json.loads(clean_json)
            
            # Helper function to format a list of points into a bulleted string.
            def format_points(points):
                if not points or not isinstance(points, list):
                    return "- Point non disponible\n- Point non disponible\n- Point non disponible"
                return '\n'.join(f"- {point}" for point in points[:3])
            
            # Format the points for each SWOT section.
            strengths = format_points(swot_data.get('forces', []))
            weaknesses = format_points(swot_data.get('faiblesses', []))
            opportunities = format_points(swot_data.get('opportunites', []))
            threats = format_points(swot_data.get('menaces', []))
        else:
            # If JSON markers are not found, raise an error.
            raise ValueError("JSON non trouvé dans la réponse")
    except Exception:
        # If parsing fails or JSON is not found, use default data.
        default_points = "- Point non disponible\n- Point non disponible\n- Point non disponible"
        strengths = weaknesses = opportunities = threats = default_points

    # Helper function to add a SWOT section (title and content) to the slide.
    def add_swot_section(slide, title, content, left, top):
        """
        Adds a textbox with a title and content for a SWOT section to the slide.

        Args:
            slide (pptx.slide.Slide): The slide object to which the text will be added.
            title (str): The title of the SWOT section (e.g., "Forces").
            content (str): The text content (e.g., bulleted list of points).
            left (float): The x-coordinate (in inches) for the left edge of the textbox.
            top (float): The y-coordinate (in inches) for the top edge of the textbox.
        """
        # Add a textbox shape to the slide at the specified position and size.
        text_box = slide.shapes.add_textbox(Inches(left), Inches(top), Inches(3), Inches(2))
        text_frame = text_box.text_frame
        
        # Add the title paragraph.
        p = text_frame.add_paragraph()
        p.text = title
        p.font.size = Pt(30)  # Set font size.
        p.font.bold = True    # Set font to bold.
        p.font.color.rgb = RGBColor(0, 0, 0) # Set font color to black.
        
        # Add the content paragraph.
        p = text_frame.add_paragraph()
        p.text = content
        p.font.size = Pt(25)  # Set font size.
        p.font.color.rgb = RGBColor(0, 0, 0) # Set font color to black.
        p.alignment = PP_ALIGN.LEFT # Align text to the left.

    # Add the four SWOT sections to the slide at their respective positions.
    add_swot_section(slide, "Forces", strengths, 0.2, 2)
    add_swot_section(slide, "Faiblesses", weaknesses, 12.7, 2)
    add_swot_section(slide, "Opportunités", opportunities, 0.2, 6)
    add_swot_section(slide, "Menaces", threats, 12.7, 6)
