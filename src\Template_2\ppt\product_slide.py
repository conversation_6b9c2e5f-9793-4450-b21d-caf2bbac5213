from pptx.util import Inc<PERSON>, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN

def add_product_slide(prs, data):
    """
    Adds the 'product/offers' slide to the presentation using Template 2 logic.

    This function populates a slide with a list of products or services offered
    by the project. It retrieves offer data from the `data` dictionary and
    displays each offer's name and price as a bulleted list. If no offers are
    found, a placeholder message is displayed.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, containing 'offers' (products/services) information.
    """
    # Access the specific slide intended for products/offers in Template 2.
    # In this template, it's assumed to be the 6th slide (index 5).
    slide = prs.slides[5]

    # Retrieve offers data from the 'data' dictionary, defaulting to an empty list.
    offers = data.get("offers", [])

    # --- Populate Slide with Offers or Placeholder ---
    if offers:
        # Define the position and size for the textbox containing product/service details.
        textbox = slide.shapes.add_textbox(Inches(5), <PERSON><PERSON>(2), <PERSON><PERSON>(10), Inches(5.5))
        text_frame = textbox.text_frame
        text_frame.word_wrap = True # Enable word wrapping.

        # Iterate through each offer and add its name and price to the textbox.
        for offer in offers:
            name = offer.get("name", "Produit sans nom") # Get offer name, default if not found.
            prix = f"{offer.get('prix_vente_ttc', 0):,.2f} DH " # Get and format price.
            sentence = f"- {name} :  {prix}." # Construct the display sentence.
            
            p = text_frame.add_paragraph() # Add a new paragraph for each offer.
            p.text = sentence             # Set the paragraph text.
            p.alignment = PP_ALIGN.LEFT   # Align text to the left.
            
            run = p.runs[0]               # Get the first run of the paragraph.
            run.font.size = Pt(35)        # Set font size.
            run.font.color.rgb = RGBColor(0, 0, 0) # Set font color to black.
    else:
        # If no offers are found, display a placeholder message.
        textbox = slide.shapes.add_textbox(Inches(5), Inches(2), Inches(7), Inches(5.5))
        text_frame = textbox.text_frame
        text_frame.text = "Aucun produit ou service trouvé dans les données."
