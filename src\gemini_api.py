"""
Gemini API abstraction module.
Handles all interactions with the Gemini API, including prompting and error handling.
"""

import google.generativeai as genai
from src.config import GENAI_API_KEY
# Configure Gemini API
genai.configure(api_key=GENAI_API_KEY)
_model = genai.GenerativeModel("gemini-2.0-flash")

def generate_content(prompt: str, model=None) -> str:
    """
    Generates content from Gemini API using the given prompt.
    Args:
        prompt (str): The prompt to send to Gemini.
        model: Optional, a custom GenerativeModel instance.
    Returns:
        str: The generated text content.
    Raises:
        Exception: If the API call fails.
    """
    try:
        used_model = model if model else _model
        response = used_model.generate_content(prompt)
        return response.text if hasattr(response, "text") else str(response)
    except Exception as e:
        # Log or handle error as needed
        print(f"Error generating content from Gemini: {e}")
        return ""

# Optionally, add more helper functions for specific prompt types as needed.
