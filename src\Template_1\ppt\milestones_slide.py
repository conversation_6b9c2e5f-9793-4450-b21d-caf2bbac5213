"""
Milestones slide module.
Responsible for rendering the milestones (jalons) section of the pitch deck.
"""

from pptx.util import <PERSON><PERSON>, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from pptx.enum.shapes import MSO_SHAPE

def add_milestones_slide(prs, data):
    """
    Adds the milestones (jalons) content to the appropriate slide(s) in the presentation.

    This function dynamically creates slides for project milestones. If no milestones
    are provided in the `data`, it adds a placeholder message. Otherwise, it calculates
    the number of slides needed based on a card layout, adds new slides if necessary,
    and then populates each slide with milestone cards, including details like name,
    amount, and date, with specific styling.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, containing 'jalons' (milestones) information.
    """
    # Retrieve milestones data from the 'data' dictionary, defaulting to an empty list.
    jalons = data.get("jalons", [])

    # If no milestones are defined, add a placeholder message to the 15th slide (index 14) and return.
    if not jalons:
        slide = prs.slides[14]
        text_box = slide.shapes.add_textbox(Inches(2), <PERSON><PERSON>(3), <PERSON><PERSON>(6), <PERSON><PERSON>(1))
        text_frame = text_box.text_frame
        text_frame.text = "Aucun jalon défini pour ce projet"
        for paragraph in text_frame.paragraphs:
            paragraph.alignment = PP_ALIGN.CENTER
            for run in paragraph.runs:
                run.font.size = Pt(18)
                run.font.color.rgb = RGBColor(255, 255, 255)
        return

    # --- Card Layout Parameters ---
    # Define dimensions and spacing for milestone cards.
    card_width = Inches(2.4)
    card_height = Inches(1.5)
    spacing = Inches(0.7)
    start_top = Inches(1.8)
    cards_per_row = 3
    rows_per_slide = 2
    cards_per_slide = cards_per_row * rows_per_slide
    # Calculate the number of slides required based on the number of milestones.
    num_slides_needed = (len(jalons) + cards_per_slide - 1) // cards_per_slide

    # Access the initial milestone slide (15th slide, index 14).
    slide15 = prs.slides[14]
    additional_slides = []
    # If more than one slide is needed, add new slides and reorder them.
    if num_slides_needed > 1:
        insert_position = 15 # Position to insert new slides.
        for i in range(1, num_slides_needed):
            slide_layout = slide15.slide_layout # Use the same layout as the initial slide.
            new_slide = prs.slides.add_slide(slide_layout) # Add a new slide.
            additional_slides.append(new_slide) # Store reference to the new slide.
            # Reorder slides to insert the new slide at the correct position.
            slides = list(prs.slides._sldIdLst)
            new_order = slides[:insert_position] + [slides[-1]] + slides[insert_position:-1]
            prs.slides._sldIdLst[:] = new_order
            insert_position += 1 # Increment insert position for the next slide.

    # --- Populate Slides with Milestone Cards ---
    # Iterate through each required slide.
    for slide_index in range(num_slides_needed):
        if slide_index == 0:
            current_slide = slide15 # Use the initial slide for the first set of milestones.
        else:
            current_slide = additional_slides[slide_index - 1] # Use additional slides for subsequent sets.
        
        # Determine the range of milestones for the current slide.
        start_index = slide_index * cards_per_slide
        end_index = min(start_index + cards_per_slide, len(jalons))
        current_jalons = jalons[start_index:end_index]

        # Iterate through the milestones for the current slide to create and style cards.
        for i, jalon in enumerate(current_jalons):
            local_index = i
            row = local_index // cards_per_row # Calculate row for card placement.
            col = local_index % cards_per_row  # Calculate column for card placement.
            # Calculate the exact position (left, top) for the current card.
            left = Inches(1.0) + col * (card_width + spacing)
            top = start_top + row * (card_height + spacing)

            # Extract milestone details, providing defaults if missing.
            nom_jalon = jalon.get("name", f"Jalon {start_index + i + 1}")
            amount = jalon.get("amount", 0)
            amount_formatted = f"{amount:,.2f} DH" if amount else "Montant non spécifié"
            date_str = jalon.get("date_delivrable", "")
            
            # Format the date string.
            if date_str:
                try:
                    if 'T' in date_str: # Handle ISO format dates.
                        date_parts = date_str.split('T')[0]
                        if '.' in date_parts: # Handle potential milliseconds.
                            date_parts = date_parts.split('.')[0]
                        date_parts = date_parts.split('-')
                        if len(date_parts) == 3: # Ensure correct date format (YYYY-MM-DD).
                            year, month, day = date_parts
                            date_formatted = f"{day}/{month}/{year}" # Format to DD/MM/YYYY.
                        else:
                            date_formatted = date_str # Fallback if parts are not 3.
                    else:
                        date_formatted = date_str # Use as is if not ISO format.
                except Exception:
                    date_formatted = date_str # Fallback for any parsing errors.
            else:
                date_formatted = "Date non spécifiée" # Default if date is missing.

            # --- Card Rectangle ---
            # Add a rounded rectangle shape for the milestone card.
            card = current_slide.shapes.add_shape(
                MSO_SHAPE.ROUNDED_RECTANGLE,
                left, top, card_width, card_height
            )
            # Apply styling to the card: solid black fill, blue border.
            card.fill.solid()
            card.fill.fore_color.rgb = RGBColor(0, 0, 0) # Black background.
            card.line.color.rgb = RGBColor(100, 149, 237) # Cornflower Blue border.
            card.line.width = Pt(1.5)

            # --- Badge (Milestone Number) ---
            badge_size = Inches(0.4)
            # Add an oval shape for the badge.
            badge = current_slide.shapes.add_shape(
                MSO_SHAPE.OVAL,
                left - badge_size/3, # Position slightly outside the card.
                top - badge_size/3,  # Position slightly outside the card.
                badge_size,
                badge_size
            )
            # Apply styling to the badge: solid white fill, blue border.
            badge.fill.solid()
            badge.fill.fore_color.rgb = RGBColor(255, 255, 255) # White background.
            badge.line.color.rgb = RGBColor(100, 149, 237) # Cornflower Blue border.
            badge.line.width = Pt(1)
            
            # Add a textbox for the badge number.
            badge_text = current_slide.shapes.add_textbox(
                left - badge_size/3,
                top - badge_size/3,
                badge_size,
                badge_size
            )
            badge_text_frame = badge_text.text_frame
            badge_text_frame.text = str(start_index + i + 1) # Set milestone number.
            # Apply font formatting to the badge number.
            for paragraph in badge_text_frame.paragraphs:
                paragraph.alignment = PP_ALIGN.CENTER # Center align text.
                for run in paragraph.runs:
                    run.font.size = Pt(12)  # Set font size.
                    run.font.bold = True    # Set font to bold.
                    run.font.color.rgb = RGBColor(0, 0, 0) # Black text.

            # --- Card Content (Name, Date, Amount) ---
            # Add a textbox for the main content of the card.
            content_box = current_slide.shapes.add_textbox(
                left + Inches(0.1), # Position slightly inside the card.
                top + Inches(0.1),  # Position slightly inside the card.
                card_width - Inches(0.2),
                card_height - Inches(0.2)
            )
            content_frame = content_box.text_frame
            content_frame.word_wrap = True # Enable word wrapping.

            # Add milestone name.
            p1 = content_frame.paragraphs[0]
            p1.text = nom_jalon
            p1.alignment = PP_ALIGN.CENTER # Center align.
            for run in p1.runs:
                run.font.size = Pt(14)  # Set font size.
                run.font.bold = True    # Set font to bold.
                run.font.color.rgb = RGBColor(255, 255, 255) # White text.
            
            # Add a separator line.
            p_space = content_frame.add_paragraph()
            p_space.text = "─────────"
            p_space.alignment = PP_ALIGN.CENTER # Center align.
            for run in p_space.runs:
                run.font.size = Pt(6)   # Smaller font size for separator.
                run.font.color.rgb = RGBColor(100, 149, 237) # Blue color for separator.
            
            # Add formatted date.
            p2 = content_frame.add_paragraph()
            p2.text = f"📅 {date_formatted}"
            p2.alignment = PP_ALIGN.CENTER # Center align.
            for run in p2.runs:
                run.font.size = Pt(10)  # Set font size.
                run.font.color.rgb = RGBColor(255, 255, 255) # White text.
            
            # Add formatted amount.
            p3 = content_frame.add_paragraph()
            p3.text = f"💰 {amount_formatted}"
            p3.alignment = PP_ALIGN.CENTER # Center align.
            for run in p3.runs:
                run.font.size = Pt(10)  # Set font size.
                run.font.color.rgb = RGBColor(255, 255, 255) # White text.
