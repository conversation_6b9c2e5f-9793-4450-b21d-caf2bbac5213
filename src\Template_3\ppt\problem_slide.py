"""
Problem slide module.
Handles the creation and formatting of the problem statement slide.
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from src.gemini_api import generate_content

def create_problem_slide(prs, data):
    """
    Creates the problem statement slide.
    
    Args:
        prs: PowerPoint presentation object
        data: Dictionary containing project data
    """
    # Generate problem text using Gemini
    prompt_problem = (
        "En tant qu'expert dans la rédaction de pitch decks, "
        f"Rédige une problématique succincte et percutante pour le projet '{data}', "
        "Structure le texte en 3 phrases courtes, sans proposer de solutions, "
        "et assure-toi que le texte soit bien lisible avec des retours à la ligne pour une présentation claire."
    )
    
    problem_text = generate_content(prompt_problem)
    wrapped_text = wrap_text(problem_text, 10)

    # Access the fourth slide (index 3)
    slide = prs.slides[3]
    
    # Add text box with problem text
    left_text = Inches(2)
    top_text = Inches(1.5)
    width_text = Inches(4)
    height_text = Inches(2)
    
    text_box = slide.shapes.add_textbox(left_text, top_text, width_text, height_text)
    text_frame = text_box.text_frame
    text_frame.text = wrapped_text
    
    # Format text
    for paragraph in text_frame.paragraphs:
        paragraph.alignment = PP_ALIGN.CENTER
        for run in paragraph.runs:
            run.font.size = Pt(14)
            run.font.bold = False
            run.font.color.rgb = RGBColor(0, 0, 0)

def wrap_text(text, words_per_line=8):
    """
    Wraps text to specified words per line.
    """
    words = text.split()
    lines = [" ".join(words[i:i + words_per_line]) for i in range(0, len(words), words_per_line)]
    return "\n".join(lines)
