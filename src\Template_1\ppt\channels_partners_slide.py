"""
Channels and Partners slide module.
Responsible for rendering the sales channels and partners section of the pitch deck.
"""

from pptx.util import <PERSON><PERSON>, Pt
from pptx.dml.color import RGBColor

def add_channels_partners_slide(prs, data):
    """
    Adds the sales channels and partners content to the appropriate slide in the presentation.

    This function populates a slide with information about sales channels and partners,
    retrieving the data from the provided `data` dictionary. It handles cases where
    data might be missing by providing default "Donnée non disponible" messages.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, containing 'canaux_ventes'
                     (sales channels) and 'partners' information.
    """
    # Access the specific slide intended for Channels and Partners.
    # In this template, it's assumed to be the 12th slide (index 11).
    slide = prs.slides[11]

    # --- Sales Channels Section ---
    # Retrieve sales channels data from the 'data' dictionary.
    # If 'canaux_ventes' key is not found, it defaults to a list containing "Donnée non disponible".
    canaux_ventes = data.get("canaux_ventes", ["Donnée non disponible"])
    # Format the sales channels into a bulleted string, taking up to the first 3 points.
    canaux_ventes_text = '\n'.join(f"- {point}" for point in canaux_ventes[:3])

    # --- Partners Section ---
    # Retrieve partners data from the 'data' dictionary. Defaults to an empty list if not found.
    partners = data.get("partners", [])
    # Format partner information into a bulleted string.
    # It iterates through partners, extracting 'name' and 'nature' if available.
    partners_text = "\n".join(
        f"- {partner['name']} ({partner['nature']})"
        for partner in partners if 'name' in partner and 'nature' in partner
    )
    # If no partners are found or formatted, set a default message.
    if not partners_text:
        partners_text = "- Donnée non disponible"

    # --- Add Sales Channels to Slide (Left Section) ---
    # Define the position and size for the sales channels textbox.
    left, top, width, height = Inches(1), Inches(2), Inches(4), Inches(2)
    # Add the textbox shape to the slide.
    text_box_channels = slide.shapes.add_textbox(left, top, width, height)
    # Get the text frame of the textbox.
    text_frame_channels = text_box_channels.text_frame
    # Enable word wrapping for the text in the textbox.
    text_frame_channels.word_wrap = True

    # Add the title for sales channels.
    p = text_frame_channels.add_paragraph()
    p.text = "Canaux de ventes :"
    p.font.size = Pt(16)  # Set font size.
    p.font.bold = True    # Set font to bold.
    p.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.

    # Add the formatted sales channels text.
    p = text_frame_channels.add_paragraph()
    p.text = canaux_ventes_text
    p.font.size = Pt(12)  # Set font size.
    p.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.

    # --- Add Partners to Slide (Right Section) ---
    # Define the position and size for the partners textbox.
    left, top, width, height = Inches(4.5), Inches(3), Inches(4), Inches(2)
    # Add the textbox shape to the slide.
    text_box_partners = slide.shapes.add_textbox(left, top, width, height)
    # Get the text frame of the textbox.
    text_frame_partners = text_box_partners.text_frame
    # Enable word wrapping for the text in the textbox.
    text_frame_partners.word_wrap = True

    # Add the title for partners.
    p = text_frame_partners.add_paragraph()
    p.text = "Partenaires :"
    p.font.size = Pt(16)  # Set font size.
    p.font.bold = True    # Set font to bold.
    p.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.

    # Add the formatted partners text.
    p = text_frame_partners.add_paragraph()
    p.text = partners_text
    p.font.size = Pt(12)  # Set font size.
    p.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.
