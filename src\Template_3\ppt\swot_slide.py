"""
SWOT slide module.
Handles the creation and formatting of the SWOT analysis slide.
"""

import json
from pptx.util import <PERSON><PERSON>, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from src.gemini_api import generate_content

def create_swot_slide(prs, data):
    """
    Creates the SWOT analysis slide.
    
    Args:
        prs: PowerPoint presentation object
        data: Dictionary containing project data
    """
    # Get slide
    slide = prs.slides[9]
    
    # Initialize default values
    default_points = "- Point non disponible\n- Point non disponible\n- Point non disponible"
    strengths = weaknesses = opportunities = threats = default_points
    
    try:
        # Generate SWOT content
        prompt_swot = f"""Analyse le projet suivant et génère une analyse SWOT très concise: {data}
        IMPORTANT: 
        1. Ta réponse doit être UNIQUEMENT un objet JSON valide
        2. Chaque point DOIT faire maximum 5-6 mots
        3. Sois direct et précis
        4. Format exact à suivre:
        {{
            "forces": ["Point 1", "Point 2", "Point 3"],
            "faiblesses": ["Point 1", "Point 2", "Point 3"],
            "opportunites": ["Point 1", "Point 2", "Point 3"],
            "menaces": ["Point 1", "Point 2", "Point 3"]
        }}"""

        response = generate_content(prompt_swot)
        if hasattr(response, 'text'):
            swot_text = response.text.strip()
        else:
            swot_text = str(response).strip()

        # Extract JSON content
        json_start = swot_text.find('{')
        json_end = swot_text.rfind('}') + 1
        if json_start != -1 and json_end != -1:
            clean_json = swot_text[json_start:json_end]
            swot_data = json.loads(clean_json)
            
            # Format SWOT sections
            def format_points(points):
                if not points or not isinstance(points, list):
                    return default_points
                return '\n'.join(f"- {point}" for point in points[:3])
            
            strengths = format_points(swot_data.get('forces', []))
            weaknesses = format_points(swot_data.get('faiblesses', []))
            opportunities = format_points(swot_data.get('opportunites', []))
            threats = format_points(swot_data.get('menaces', []))
            
            print("✅ Analyse SWOT générée avec succès")
        else:
            raise ValueError("JSON non trouvé dans la réponse")
            
    except Exception as e:
        print(f"❌ Erreur lors de la génération SWOT: {str(e)}")

    # Add SWOT sections to slide
    add_swot_section(slide, "Forces", strengths, 0.2, 1.7)
    add_swot_section(slide, "Faiblesses", weaknesses, 6, 1.7)
    add_swot_section(slide, "Opportunités", opportunities, 0.2, 3.5)
    add_swot_section(slide, "Menaces", threats, 6, 3.5)

def add_swot_section(slide, title, content, left, top):
    """Add a SWOT section to the slide with title and content."""
    text_box = slide.shapes.add_textbox(Inches(left), Inches(top), Inches(3), Inches(2))
    text_frame = text_box.text_frame
    
    # Add title
    p = text_frame.add_paragraph()
    p.text = title
    p.font.size = Pt(20)
    p.font.bold = True
    p.font.color.rgb = RGBColor(0, 0, 0)
    
    # Add content
    p = text_frame.add_paragraph()
    p.text = content
    p.font.size = Pt(14)
    p.font.color.rgb = RGBColor(0, 0, 0)
    p.alignment = PP_ALIGN.LEFT
