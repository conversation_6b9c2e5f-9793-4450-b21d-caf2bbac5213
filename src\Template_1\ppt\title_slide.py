"""
Title slide module.
Responsible for rendering the main title and project name on the first slide of the pitch deck.
"""

from pptx.util import <PERSON><PERSON>, Pt
from pptx.dml.color import RGBColor

def add_title_slide(prs, data):
    """
    Adds the main title ("PITCHDECK") and the project name to the first slide of the presentation.

    This function retrieves the project name from the `data` dictionary and then
    creates a text box on the title slide to display both the generic "PITCHDECK"
    title and the specific project name, applying consistent font styling.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, containing the 'name' of the project.
    """
    # Retrieve the project name from the 'data' dictionary, defaulting to "Projet Inconnu".
    nom_projet = data.get("name", "Projet Inconnu")
    
    # Access the first slide of the presentation (index 0).
    slide = prs.slides[0]
    
    # Define the position and size for the title text box.
    left_text = Inches(5)
    top_text = Inches(1.5)
    width_text = Inches(3.5)
    height_text = Inches(2)
    
    # Add a textbox shape to the slide at the specified position and size.
    text_box = slide.shapes.add_textbox(left_text, top_text, width_text, height_text)
    # Get the text frame of the textbox.
    text_frame = text_box.text_frame
    
    # Set the text content, combining "PITCHDECK" and the project name with a newline.
    text_frame.text = f"PITCHDECK\n{nom_projet}"
    
    # Apply font formatting to all paragraphs and runs within the text frame.
    for paragraph in text_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(40)  # Set font size to 40 points.
            run.font.bold = True    # Set font to bold.
            run.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.
