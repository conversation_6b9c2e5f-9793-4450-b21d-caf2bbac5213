from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.enum.shapes import MSO_SHAPE

def add_team_slide(prs, data):
    """
    Adds the team slide to the presentation using Template 2 logic.

    This function dynamically populates the team slide with information about
    each team member's position. It arranges team members in columns of
    rounded rectangles, applying specific styling for visual presentation.
    If no team data is available, a placeholder message is displayed.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, containing 'equipes' (team members) information.
    """
    # Access the specific slide intended for the team section in Template 2.
    # In this template, it's assumed to be the 8th slide (index 7).
    slide = prs.slides[7]
    # Retrieve team data from the 'data' dictionary, defaulting to an empty list.
    equipes_data = data.get("equipes", [])

    # --- Layout Parameters for Team Member Shapes ---
    right_side_left = Inches(8) # Starting left position for the team member shapes.
    initial_top = Inches(2.5)   # Starting top position for the team member shapes.
    shape_width = Inches(6)     # Width of each team member shape (used for placeholder).
    shape_height = Inches(1.5)  # Height of each team member shape (used for placeholder).
    vertical_gap = Inches(0.5)  # Vertical spacing between team member shapes.
    current_top = initial_top   # Tracks the current top position for placing shapes.

    # --- Populate Slide with Team Members or Placeholder ---
    if equipes_data:
        members_per_column = 5 # Maximum number of team members to display per column.
        # Calculate the number of columns needed based on total members and members per column.
        num_columns = (len(equipes_data) + members_per_column - 1) // members_per_column
        # Calculate the width of each column, ensuring it's at least Inches(5.5) if no columns.
        column_width = Inches(5.5) / num_columns if num_columns > 0 else Inches(5.5)
        column_gap = Inches(0.5) # Horizontal gap between columns.

        # Iterate through each column.
        for col in range(num_columns):
            # Calculate the left position for the current column.
            column_left = right_side_left + (column_width + column_gap) * col
            current_top = initial_top # Reset top position for each new column.

            # Iterate through members within the current column.
            for i in range(members_per_column):
                member_index = col * members_per_column + i # Calculate global index of the member.
                if member_index >= len(equipes_data):
                    break # Stop if all members have been processed.

                membre = equipes_data[member_index] # Get the current team member's data.
                post_name = membre.get("post_name", "Poste non spécifié") # Get position name.

                # Add a rounded rectangle shape for the team member.
                shape = slide.shapes.add_shape(
                    MSO_SHAPE.ROUNDED_RECTANGLE,
                    column_left,
                    current_top,
                    column_width,
                    shape_height
                )

                # Apply styling to the shape: light gray fill, dark slate gray border.
                fill = shape.fill
                fill.solid()
                fill.fore_color.rgb = RGBColor(240, 240, 240) # Light gray background.
                line = shape.line
                line.color.rgb = RGBColor(47, 79, 79) # Dark Slate Gray border.
                line.width = Pt(1.5)

                # Configure text frame for centered text, proper margins, and word wrapping.
                text_frame = shape.text_frame
                text_frame.margin_left = Inches(0.15)
                text_frame.margin_right = Inches(0.15)
                text_frame.vertical_anchor = MSO_ANCHOR.MIDDLE # Vertically center text.
                text_frame.word_wrap = True

                text_frame.text = post_name # Set the text to the team member's position.
                # Apply font formatting to the position text.
                for paragraph in text_frame.paragraphs:
                    paragraph.alignment = PP_ALIGN.CENTER # Center align text.
                    for run in paragraph.runs:
                        run.font.size = Pt(24)  # Set font size.
                        run.font.bold = True    # Set font to bold.
                        run.font.color.rgb = RGBColor(0, 0, 0) # Black text.

                shape.height = Inches(0.8) # Adjust shape height after text is added.
                current_top += Inches(0.8) + vertical_gap # Move down for the next shape.
    else:
        # If no team data is available, display a placeholder message.
        textbox = slide.shapes.add_textbox(right_side_left, initial_top, shape_width, Inches(1))
        text_frame = textbox.text_frame
        p = text_frame.add_paragraph()
        p.text = "Aucune donnée d'équipe disponible."
        p.font.size = Pt(18)
        p.font.color.rgb = RGBColor(100, 100, 100) # Gray text.
