from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
import json
from pptx.enum.text import PP_ALIGN
from src.gemini_api import generate_content

def add_concurrence_slide(prs, data):
    """
    Adds the competition/concurrence slide to the presentation using Template 2 logic.

    This function generates competitive analysis data using Google's Gemini AI,
    parses the AI's JSON response, and then populates the competitive analysis slide
    in the PowerPoint presentation with the generated data. It compares "our project"
    with "competitors" based on key points, displaying them in two columns.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, used as input for AI content generation.
    """
    # Access the specific slide intended for competitive analysis in Template 2.
    # In this template, it's assumed to be the 10th slide (index 9).
    slide = prs.slides[9]

    # --- Generate Competitive Analysis with Gemini AI ---
    # Define the prompt for Gemini AI to generate competitive analysis in JSON format.
    # The prompt specifies strict JSON output, maximum word count per point, and directness.
    prompt_concurrence = (
        f"Compare notre projet avec ceux du même secteur en suivant ce format:\n"
        "IMPORTANT: \n"
        "1. Réponds UNIQUEMENT en JSON valide\n"
        "2. Chaque point DOIT faire maximum 5-6 mots\n"
        "3. Sois direct et précis\n"
        "4. Format exact à suivre:\n"
        "{\n"
        '    "them": [\n'
        '        "Premier point clé des concurrents",\n'
        '        "Deuxième point clé des concurrents",\n'
        '        "Troisième point clé des concurrents"\n'
        "    ],\n"
        '    "us": [\n'
        '        "Premier point clé de notre projet",\n'
        '        "Deuxième point clé de notre projet",\n'
        '        "Troisième point clé de notre projet"\n'
        "    ]\n"
        "}\n"
        f"Voici notre projet : {data}"
    )

    # Generate content using the Gemini API.
    response_concurrence = generate_content(prompt_concurrence)
    # Strip any leading/trailing whitespace from the raw AI response.
    concurrence_text = response_concurrence.strip()

    # --- Parse JSON Response ---
    try:
        # Find the start and end of the JSON object within the AI's response.
        json_start = concurrence_text.find('{')
        json_end = concurrence_text.rfind('}') + 1
        if json_start != -1 and json_end != -1:
            # Extract the clean JSON string.
            clean_json = concurrence_text[json_start:json_end]
            # Load the JSON string into a Python dictionary.
            concurrence_data = json.loads(clean_json)
            
            # Helper function to format a list of points into a bulleted string.
            def format_points(points):
                if not points or not isinstance(points, list):
                    return "- Donnée non disponible\n- Donnée non disponible\n- Donnée non disponible"
                return '\n'.join(f"- {point}" for point in points[:3])
            
            # Format the "them" (competitors) and "us" (our project) points.
            them = format_points(concurrence_data.get('them', []))
            us = format_points(concurrence_data.get('us', []))
        else:
            # If JSON markers are not found, raise an error.
            raise ValueError("JSON non trouvé dans la réponse")
    except Exception:
        # If parsing fails or JSON is not found, use default data.
        default_points = "- Donnée non disponible\n- Donnée non disponible\n- Donnée non disponible"
        them, us = default_points, default_points

    # Helper function to add a section of competitive analysis content to the slide.
    def add_concurrence_section(slide, content, left, top):
        """
        Adds a textbox with competitive analysis content to the slide.

        Args:
            slide (pptx.slide.Slide): The slide object to which the text will be added.
            content (str): The text content (e.g., bulleted list of points).
            left (float): The x-coordinate (in inches) for the left edge of the textbox.
            top (float): The y-coordinate (in inches) for the top edge of the textbox.
        """
        # Add a textbox shape to the slide at the specified position and size.
        text_box = slide.shapes.add_textbox(Inches(left), Inches(top), Inches(6), Inches(6))
        text_frame = text_box.text_frame
        text_frame.word_wrap = True # Enable word wrapping.
        p = text_frame.add_paragraph()
        p.text = content
        p.font.size = Pt(30)  # Set font size.
        p.font.bold = False   # Set font to not bold.
        p.font.color.rgb = RGBColor(0, 0, 0) # Set font color to black.
        p.alignment = PP_ALIGN.LEFT # Align text to the left.

    # Add the "them" (competitors) points to the left side of the slide.
    add_concurrence_section(slide, them, 2.2, 4.2)
    # Add the "us" (our project) points to the right side of the slide.
    add_concurrence_section(slide, us, 11.9, 4.2)
