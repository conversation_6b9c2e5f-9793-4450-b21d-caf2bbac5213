"""
Solution slide module.
Responsible for rendering the solution section of the pitch deck.
"""

from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from src.gemini_api import generate_content
from src.Template_1.text_utils import wrap_text

def add_solution_slide(prs, data):
    """
    Adds the solution content to the appropriate slide in the presentation.

    This function generates a concise and impactful solution description for the project
    using Google's Gemini AI, formats it for readability, and then adds it to the
    solution slide in the PowerPoint presentation, along with a title.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, used as input for AI content generation.
    """
    # --- Generate Solution Text with Gemini AI ---
    # Define the prompt for Gemini AI to generate a succinct and impactful solution description.
    # The prompt specifies a 3-sentence structure, focusing on innovative approaches and benefits.
    prompt_solution = (
        "En tant qu'expert dans la rédaction de pitch decks, "
        f"Rédige une solution concise et percutante pour le projet '{data}', "
        "en exposant clairement les approches innovantes et les bénéfices de cette solution, sans entrer dans les détails techniques. "
        "Structure le texte en 3 phrases courtes, en utilisant des retours à la ligne pour la lisibilité."
    )
    # Generate content using the Gemini API.
    solution_raw = generate_content(prompt_solution)
    # Wrap the generated text to ensure it fits within the slide's layout, with a line limit of 8.
    solution_text = wrap_text(solution_raw, 8)

    # Access the specific slide intended for the solution.
    # In this template, it's assumed to be the 5th slide (index 4).
    slide = prs.slides[4]

    # --- Add the Title "Solution" to the Slide ---
    # Define the position and size for the title textbox.
    left, top, width, height = Inches(2.5), Inches(1.5), Inches(4), Inches(1)
    # Add the textbox shape for the title.
    title_box = slide.shapes.add_textbox(left, top, width, height)
    # Get the text frame of the title textbox.
    title_frame = title_box.text_frame
    # Set the title text.
    title_frame.text = "Solution"
    # Apply font formatting to all paragraphs and runs within the title text frame.
    for paragraph in title_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(40)  # Set font size.
            run.font.bold = True    # Set font to bold.
            run.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.

    # --- Add the Solution Content under the Title ---
    # Define the position and size for the content textbox.
    left, top, width, height = Inches(2.5), Inches(2.5), Inches(6), Inches(3)
    # Add the textbox shape for the content.
    content_box = slide.shapes.add_textbox(left, top, width, height)
    # Get the text frame of the content textbox.
    content_frame = content_box.text_frame
    # Set the solution content.
    content_frame.text = solution_text
    # Apply font formatting and alignment to all paragraphs and runs within the content text frame.
    for paragraph in content_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(15)  # Set font size.
            run.font.bold = False   # Set font to not bold.
            run.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.
        paragraph.alignment = PP_ALIGN.JUSTIFY # Justify align the text.
