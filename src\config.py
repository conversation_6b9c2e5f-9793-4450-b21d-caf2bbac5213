"""
Configuration module for pitch deck generation.
Centralizes API keys, file paths, and other global settings.

Note: For production, store sensitive keys in environment variables or a .env file.
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Gemini API Key (now loaded from environment variable)
GENAI_API_KEY = os.getenv("GENAI_API_KEY", "")

# File paths
PPT_TEMPLATE_PATH = "ppt_templates/pitchdeck1.pptx"
PPT_TEMPLATE_2_PATH = "ppt_templates/pitchdeck2.pptx"
PPT_TEMPLATE_3_PATH = "ppt_templates/pitchdeck3.pptx"
DOC_TEMPLATE_PATH = "doc_templates/Doc1.docx"

