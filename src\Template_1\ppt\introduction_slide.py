"""
Introduction slide module.
Responsible for rendering the introduction section of the pitch deck.
"""

from pptx.util import <PERSON><PERSON>, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from src.gemini_api import generate_content
from src.Template_1.text_utils import wrap_text

def add_introduction_slide(prs, data):
    """
    Adds the introduction content to the appropriate slide in the presentation.

    This function generates an introduction text for the project using Google's Gemini AI,
    formats it for readability, and then adds it to the introduction slide in the
    PowerPoint presentation, along with a title.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, containing project information
                     like 'name', used as input for AI content generation.
    """
    # --- Get Project Name and Generate Introduction Text ---
    # Retrieve the project name from the 'data' dictionary, defaulting to "Projet Inconnu".
    nom_projet = data.get("name", "Projet Inconnu")

    # Define the prompt for Gemini AI to generate a concise and impactful introduction.
    # The prompt specifies the desired structure (4 short sentences with line breaks).
    prompt_intro = (
        f"Rédige une introduction concise et percutante pour le projet '{data}' "
        "en mettant en avant son objectif principal et son impact. "
        "Fais en sorte que le texte soit bien structuré en 4 phrases courtes, "
        "avec des retours à la ligne pour une meilleure lisibilité."
    )
    # Generate content using the Gemini API.
    introduction_raw = generate_content(prompt_intro)
    # Wrap the generated text to ensure it fits within the slide's layout, with a line limit of 8.
    introduction_text = wrap_text(introduction_raw, 8)

    # Access the specific slide intended for the introduction.
    # In this template, it's assumed to be the 3rd slide (index 2).
    slide = prs.slides[2]

    # --- Add the Title "Introduction" to the Slide ---
    # Define the position and size for the title textbox.
    left, top, width, height = Inches(0.5), Inches(1.5), Inches(6), Inches(2)
    # Add the textbox shape for the title.
    title_box = slide.shapes.add_textbox(left, top, width, height)
    # Get the text frame of the title textbox.
    title_frame = title_box.text_frame
    # Set the title text.
    title_frame.text = "Introduction"
    # Apply font formatting to all paragraphs and runs within the title text frame.
    for paragraph in title_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(40)  # Set font size.
            run.font.bold = True    # Set font to bold.
            run.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.

    # --- Add the Introduction Content under the Title ---
    # Define the position and size for the content textbox.
    left, top, width, height = Inches(0.5), Inches(2.5), Inches(4), Inches(2)
    # Add the textbox shape for the content.
    content_box = slide.shapes.add_textbox(left, top, width, height)
    # Get the text frame of the content textbox.
    content_frame = content_box.text_frame
    # Set the introduction content.
    content_frame.text = introduction_text
    # Apply font formatting to all paragraphs and runs within the content text frame.
    for paragraph in content_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(15)  # Set font size.
            run.font.bold = False   # Set font to not bold.
            run.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.
