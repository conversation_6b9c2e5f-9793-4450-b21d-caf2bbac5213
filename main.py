"""
Main orchestrator script for pitch deck generation.
Loads data, calls Gemini API, and builds the presentation using modular slide functions.
"""

import importlib
import os
from pptx import Presentation
from src.config import PPT_TEMPLATE_PATH, PPT_TEMPLATE_2_PATH, PPT_TEMPLATE_3_PATH,DOC_TEMPLATE_PATH
from doc import generate_document_from_data 

def generate_ppt(template_number, data, output_path):
    # Select the correct PowerPoint template path
    if str(template_number) == "1":
        ppt_template_path = PPT_TEMPLATE_PATH
    elif str(template_number) == "2":
        ppt_template_path = PPT_TEMPLATE_2_PATH
    else:
        ppt_template_path = PPT_TEMPLATE_3_PATH

    # Load PowerPoint template
    prs = Presentation(ppt_template_path)

    # Dynamically import slide modules based on template number
    ppt_module_path = f"src.Template_{template_number}.ppt"

    # Define slide function names for each template
    if str(template_number) == "1":
        slide_functions = [
            "add_title_slide",
            "add_introduction_slide",
            "add_problem_slide",
            "add_solution_slide",
            "add_swot_slide",
            "add_project_slide",
            "add_client_slide",
            "add_team_slide",
            "add_concurrence_slide",
            "add_channels_partners_slide",
            "add_business_model_slide",
            "add_finance_slide",
            "add_milestones_slide",
            "add_product_slide",
            "add_thanks_slide",
        ]
    elif str(template_number) == "2":
        slide_functions = [
            "add_title_slide",
            "add_introduction_slide",
            "add_problem_slide",
            "add_solution_slide",
            "add_product_slide",
            "add_client_slide",
            "add_team_slide",
            "add_business_model_slide",
            "add_concurrence_slide",
            "add_swot_slide",
            "add_finance_slide",
            "add_milestones_slide",
            "add_funding_request_slide",
            "add_thanks_slide",
        ]
    else:  # Template 3
        slide_functions = [
            "create_title_slide",
            "create_introduction_slide",
            "create_problem_slide",
            "create_solution_slide",
            "create_client_slide",
            "create_product_slide",
            "create_business_model_slide",
            "create_concurrence_slide",
            "create_swot_slide",
            "create_team_slide",
            "create_funding_request_slide",
            "create_milestones_slide",
            "create_thanks_slide"
        ]

    # Import and call each slide function in order
    for func_name in slide_functions:
        module_name = func_name.replace("create_", "").replace("add_", "").replace("_slide", "") + "_slide"
        try:
            slide_module = importlib.import_module(f"{ppt_module_path}.{module_name}")
            slide_func = getattr(slide_module, func_name)
            slide_func(prs, data)
        except (ModuleNotFoundError, AttributeError) as e:
            print(f"Warning: Could not import or call {func_name} from {ppt_module_path}.{module_name}: {e}")

    # Ensure the output directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Save the generated presentation locally
    prs.save(output_path)
    print(f"✅ Pitch deck generated and saved to {output_path}")
    return output_path

def generate_doc(data, output_path):
    """
    Generate a document based on the provided data and save it to the specified path.
    
    Args:
        data (dict): The data to use for document generation
        output_path (str): Path where the generated document will be saved
        
    Returns:
        str: Path to the generated document
    """
    
    # Ensure the output directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Call the refactored function from doc.py
    generate_document_from_data(data, output_path, DOC_TEMPLATE_PATH)
    
    print(f"✅ Document generated and saved to {output_path}")
    return output_path
