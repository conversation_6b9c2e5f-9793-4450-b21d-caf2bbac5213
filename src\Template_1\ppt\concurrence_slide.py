"""
Concurrence (Competitive Analysis) slide module.
Responsible for rendering the competitive analysis section of the pitch deck.
"""

import json
from pptx.util import <PERSON><PERSON>, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from src.gemini_api import generate_content

def add_concurrence_slide(prs, data):
    """
    Adds the competitive analysis content to the appropriate slide in the presentation.

    This function generates competitive analysis data using Google's Gemini AI,
    parses the AI's JSON response, and then populates the competitive analysis slide
    in the PowerPoint presentation with the generated data. It compares "our project"
    with "competitors" based on key points. If the AI response is invalid, it falls
    back to default "Donnée non disponible" data.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, used as input for AI content generation.
    """
    # --- Generate Competitive Analysis with Gemini AI ---
    # Define the prompt for Gemini AI to generate competitive analysis in JSON format.
    # The prompt specifies strict JSON output, maximum word count per point, and directness.
    prompt_concurrence = (
        "Compare notre projet avec ceux du même secteur en suivant ce format:\n"
        "IMPORTANT:\n"
        "1. Réponds UNIQUEMENT en JSON valide\n"
        "2. Chaque point DOIT faire maximum 5-6 mots\n"
        "3. Sois direct et précis\n"
        "4. Format exact à suivre:\n"
        "{\n"
        '    "them": ["Premier point clé des concurrents", "Deuxième point clé des concurrents", "Troisième point clé des concurrents"],\n'
        '    "us": ["Premier point clé de notre projet", "Deuxième point clé de notre projet", "Troisième point clé de notre projet"]\n'
        "}\n"
        f"Voici notre projet : {data}"
    )
    # Generate content using the Gemini API based on the defined prompt.
    concurrence_raw = generate_content(prompt_concurrence)
    # Strip any leading/trailing whitespace from the raw AI response.
    concurrence_text = concurrence_raw.strip()

    # --- Parse JSON Response ---
    try:
        # Find the start and end of the JSON object within the AI's response.
        json_start = concurrence_text.find('{')
        json_end = concurrence_text.rfind('}') + 1
        if json_start != -1 and json_end != -1:
            # Extract the clean JSON string.
            clean_json = concurrence_text[json_start:json_end]
            # Load the JSON string into a Python dictionary.
            concurrence_data = json.loads(clean_json)
        else:
            # If JSON markers are not found, raise an error.
            raise ValueError("JSON non trouvé dans la réponse")
    except Exception:
        # If parsing fails or JSON is not found, use default data.
        # This ensures the slide can still be generated even if AI response is problematic.
        concurrence_data = {
            "them": ["Donnée non disponible"] * 3,
            "us": ["Donnée non disponible"] * 3,
        }

    # Helper function to format a list of points into a bulleted string.
    def format_points(points):
        """
        Formats a list of strings into a bulleted list, taking up to the first 3 points.

        Args:
            points (list): A list of strings to be formatted.

        Returns:
            str: A string with each point prefixed by a bullet and separated by newlines.
        """
        return '\n'.join(f"- {point}" for point in points[:3])

    # Format the "them" (competitors) and "us" (our project) points.
    them = format_points(concurrence_data.get('them', []))
    us = format_points(concurrence_data.get('us', []))

    # Access the specific slide intended for competitive analysis.
    # In this template, it's assumed to be the 10th slide (index 9).
    slide = prs.slides[9]

    # Helper function to add a section of competitive analysis content to the slide.
    def add_concurrence_section(slide, content, left, top):
        """
        Adds a textbox with competitive analysis content to the slide.

        Args:
            slide (pptx.slide.Slide): The slide object to which the text will be added.
            content (str): The text content to be added (e.g., bulleted list of points).
            left (float): The x-coordinate (in inches) for the left edge of the textbox.
            top (float): The y-coordinate (in inches) for the top edge of the textbox.
        """
        # Add a textbox shape to the slide at the specified position and size.
        text_box = slide.shapes.add_textbox(Inches(left), Inches(top), Inches(4), Inches(2))
        # Get the text frame of the textbox.
        text_frame = text_box.text_frame
        # Enable word wrapping for the text.
        text_frame.word_wrap = True
        # Add a paragraph and set its text content.
        p = text_frame.add_paragraph()
        p.text = content
        p.font.size = Pt(12)  # Set font size.
        p.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.
        p.alignment = PP_ALIGN.LEFT # Align text to the left.

    # Add the "them" (competitors) points to the left side of the slide.
    add_concurrence_section(slide, them, 1.5, 3)
    # Add the "us" (our project) points to the right side of the slide.
    add_concurrence_section(slide, us, 5.5, 3)
