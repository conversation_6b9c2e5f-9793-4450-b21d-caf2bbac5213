"""
Business Model Canvas slide module.
Responsible for rendering the Business Model Canvas section of the pitch deck.
"""

import json
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from src.gemini_api import generate_content

def add_business_model_slide(prs, data):
    """
    Adds the Business Model Canvas content to the appropriate slide in the presentation.

    This function generates Business Model Canvas (BMC) content using Google's Gemini AI,
    parses the AI's JSON response, and then populates the BMC slide in the PowerPoint
    presentation with the generated data. If the AI response is invalid, it falls back
    to default BMC data.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, used as input for AI content generation.
    """
    # Define the prompt for Gemini AI to generate Business Model Canvas content in JSON format.
    # The prompt instructs the AI to act as a business model expert and generate detailed,
    # relevant content for each BMC section, with a specific format and length constraint.
    prompt_bmc = (
        "En tant qu'expert en business model, analyse le projet suivant : {data}\n"
        "Génère un Business Model Canvas détaillé et pertinent sous forme de JSON strict.\n"
        "Retourne UNIQUEMENT le JSON suivant rempli avec un contenu pertinent (3-4-5 éléments par section et 3-4 mots par élément) :\n"
        "{{\n"
        '    "partenaires_cles": [],\n'
        '    "activites_cles": [],\n'
        '    "ressources_cles": [],\n'
        '    "proposition_valeur": [],\n'
        '    "relation_client": [],\n'
        '    "canaux": [],\n'
        '    "segments_client": [],\n'
        '    "structure_couts": [],\n'
        '    "revenus": []\n'
        "}}"
    ).format(data=data)

    # Generate content using the Gemini API based on the defined prompt.
    bmc_raw = generate_content(prompt_bmc)
    # Strip any leading/trailing whitespace from the raw AI response.
    bmc_text = bmc_raw.strip()

    # Parse the JSON response from Gemini.
    try:
        # Find the start and end of the JSON object within the AI's response.
        json_start = bmc_text.find('{')
        json_end = bmc_text.rfind('}') + 1
        if json_start != -1 and json_end != -1:
            # Extract the clean JSON string.
            clean_json = bmc_text[json_start:json_end]
            # Load the JSON string into a Python dictionary.
            bmc_data = json.loads(clean_json)
        else:
            # If JSON markers are not found, raise an error.
            raise ValueError("JSON non trouvé dans la réponse")
    except Exception:
        # If parsing fails or JSON is not found, use default BMC data.
        # This ensures the slide can still be generated even if AI response is problematic.
        bmc_data = {
            "partenaires_cles": ["Fournisseurs clés", "Partenaires stratégiques", "Distributeurs"],
            "activites_cles": ["Développement", "Marketing", "Support client"],
            "ressources_cles": ["Équipe technique", "Infrastructure", "Propriété intellectuelle"],
            "proposition_valeur": ["Innovation", "Qualité", "Service client"],
            "relation_client": ["Support dédié", "Communauté", "Formation"],
            "canaux": ["Web", "Mobile", "Partenaires"],
            "segments_client": ["Entreprises", "Particuliers", "Institutions"],
            "structure_couts": ["R&D", "Marketing", "Opérations"],
            "revenus": ["Abonnements", "Services", "Licences"]
        }

    # Helper function to add text content to specific sections of the BMC slide.
    def add_bmc_text(slide, text_list, left, top, width=Inches(2), height=Inches(2)):
        """
        Adds a list of text items to a textbox on the slide at specified coordinates.

        Args:
            slide (pptx.slide.Slide): The slide object to which the text will be added.
            text_list (list): A list of strings, where each string represents an item
                              to be added as a bullet point.
            left (float): The x-coordinate (in inches) for the left edge of the textbox.
            top (float): The y-coordinate (in inches) for the top edge of the textbox.
            width (pptx.util.Inches): The width of the textbox.
            height (pptx.util.Inches): The height of the textbox.
        """
        # Add a new textbox shape to the slide at the specified position and size.
        textbox = slide.shapes.add_textbox(Inches(left), Inches(top), width, height)
        # Get the text frame of the newly added textbox.
        text_frame = textbox.text_frame
        # Clear any existing text in the text frame (important if reusing a textbox).
        if text_frame.text:
            text_frame.clear()
        # Iterate through the list of text items to add them as bullet points.
        for i, item in enumerate(text_list):
            if i == 0:
                # For the first item, use the default first paragraph.
                paragraph = text_frame.paragraphs[0]
            else:
                # For subsequent items, add a new paragraph.
                paragraph = text_frame.add_paragraph()
            # Set the text of the paragraph, prepending a bullet point.
            paragraph.text = "• " + item
            # Apply consistent font formatting to all runs within the paragraph.
            for run in paragraph.runs:
                run.font.size = Pt(11)  # Set font size to 11 points.
                run.font.color.rgb = RGBColor(0, 0, 0)  # Set font color to black.
                run.font.name = "Calibri"  # Set font name to Calibri.

    # Access the specific slide intended for the Business Model Canvas.
    # In this template, it's assumed to be the 13th slide (index 12).
    slide = prs.slides[12]

    # Add content to each section of the Business Model Canvas using the helper function.
    # Each call specifies the slide, the relevant data list from bmc_data, and the
    # x, y coordinates for positioning the text box on the slide.
    add_bmc_text(slide, bmc_data["partenaires_cles"], 0.06, 0.5)
    add_bmc_text(slide, bmc_data["activites_cles"], 2, 0.5)
    add_bmc_text(slide, bmc_data["ressources_cles"], 2, 2.1)
    add_bmc_text(slide, bmc_data["proposition_valeur"], 4, 0.5)
    add_bmc_text(slide, bmc_data["relation_client"], 6, 0.5)
    add_bmc_text(slide, bmc_data["canaux"], 6, 2.1)
    add_bmc_text(slide, bmc_data["segments_client"], 8, 0.5)
    add_bmc_text(slide, bmc_data["structure_couts"], 0.2, 3.8, width=Inches(5), height=Inches(1.5))
    add_bmc_text(slide, bmc_data["revenus"], 5.3, 3.8, width=Inches(5), height=Inches(1.5))
