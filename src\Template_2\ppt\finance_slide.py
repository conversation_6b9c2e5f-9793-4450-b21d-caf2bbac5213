from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
import matplotlib.pyplot as plt
from io import BytesIO

def add_finance_slide(prs, data):
    """
    Adds the finance/resources slide to the presentation using Template 2 logic.

    This function extracts financial resource data from the provided `data` dictionary,
    calculates the total financing, and then generates a donut chart using Matplotlib
    to visualize the distribution of these resources. The chart and a legend are
    then added to the finance slide. If no valid financial data is found, a placeholder
    message is displayed instead.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, containing financial information.
    """
    # Access the specific slide intended for finance/resources in Template 2.
    # In this template, it's assumed to be the 12th slide (index 11).
    slide = prs.slides[11]

    # --- Extract Funding Data ---
    ressources = {}
    # Attempt to extract 'ressources_data' from 'volume_vente' if it exists and is structured as expected.
    if "volume_vente" in data and isinstance(data["volume_vente"], list) and len(data["volume_vente"]) > 3:
        potential_ressources = data["volume_vente"][3]
        if isinstance(potential_ressources, dict) and "ressources_data" in potential_ressources:
            ressources = potential_ressources.get("ressources_data", {})
            print("✅ Extraction réussie des données de financement depuis 'volume_vente'.")
        else:
            # Fallback if 'volume_vente[3]' exists but doesn't contain 'ressources_data'.
            ressources = data.get("ressources_data", {})
            print("✅ Extraction réussie des données de financement directement depuis 'data' (volume_vente[3] did not contain ressources_data).")
    else:
        # Default extraction directly from 'data' if 'volume_vente' structure is not as expected.
        ressources = data.get("ressources_data", {})
        print("✅ Extraction réussie des données de financement directement depuis 'data'.")
            
    # Retrieve specific funding amounts, defaulting to 0 if not found.
    apport = ressources.get("Apports_des_associes", 0)
    pret_subvention = ressources.get("Prets_et_subvention", 0)
    financement_akkan = ressources.get("Crowdfunding_akkan", 0)
    # Calculate the total financing.
    total_financement = apport + pret_subvention + financement_akkan

    # --- Generate and Add Donut Chart if Valid Data Exists ---
    if total_financement > 0:
        # Define labels, sizes (amounts), and colors for the pie chart segments.
        labels = ["Apports des associés", "Prêts et subventions", "Crowdfunding Akkan"]
        sizes = [apport, pret_subvention, financement_akkan]
        colors = ['#cccccc', '#ff9999', '#008080'] # Gray, Light Red, Teal.

        # Create a Matplotlib figure and axes for the pie chart.
        fig, ax = plt.subplots(figsize=(6, 6))
        # Create the donut chart.
        ax.pie(
            sizes,
            colors=colors,
            autopct='%1.1f%%', # Format percentage labels.
            startangle=90,     # Start angle for the first slice.
            wedgeprops={'width': 0.4} # Make it a donut chart.
        )
        # Add a central text label to the donut hole.
        ax.text(0, 0, 'RESSOURCES\nFINANCIÈRES', ha='center', va='center',
                fontsize=16, color='black')
        ax.axis('equal') # Ensure the pie chart is circular.

        # Save the chart to a BytesIO object as a PNG image.
        image_stream = BytesIO()
        plt.savefig(image_stream, format='png', bbox_inches='tight')
        plt.close() # Close the plot to free up memory.
        image_stream.seek(0) # Reset stream position to the beginning.

        # Add the generated chart image to the slide.
        left = Inches(6)
        top = Inches(3)
        height = Inches(7)
        slide.shapes.add_picture(image_stream, left, top, height=height)

        # --- Add Legend/Textual Breakdown of Resources ---
        text_left = Inches(0.5)
        text_top = Inches(9)
        text_width = Inches(7)
        text_height = Inches(5)

        # Add a textbox for the legend.
        text_box = slide.shapes.add_textbox(text_left, text_top, text_width, text_height)
        text_frame = text_box.text_frame
        text_frame.word_wrap = True # Enable word wrapping.

        # Add each resource label and amount to the legend, with corresponding colors.
        for i, (label, size, color) in enumerate(zip(labels, sizes, colors)):
            p = text_frame.add_paragraph()
            p.text = f"{label}: {size} DH"
            p.font.size = Pt(25)  # Set font size.
            p.font.bold = True    # Set font to bold.
            # Convert hex color string to RGB tuple for font color.
            p.font.color.rgb = RGBColor(*[int(color[i:i+2], 16) for i in (1, 3, 5)])
    else:
        # If no valid financial data, display a placeholder message.
        text_box = slide.shapes.add_textbox(Inches(1), Inches(5), Inches(8), Inches(2))
        text_frame = text_box.text_frame
        text_frame.text = "Aucune donnée valide à afficher pour les ressources financières."
