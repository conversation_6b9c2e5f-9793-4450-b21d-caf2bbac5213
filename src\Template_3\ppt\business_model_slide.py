"""
Business Model slide module.
Handles the creation and formatting of the Business Model Canvas slide.
"""

import json
from pptx.util import <PERSON>hes, Pt
from pptx.dml.color import RGBColor
from src.gemini_api import generate_content

def create_business_model_slide(prs, data):
    """
    Creates the Business Model Canvas slide.
    
    Args:
        prs: PowerPoint presentation object
        data: Dictionary containing project data
        slide_index: Index of the BMC slide (default=7)
    """
    # Initialize default BMC data
    bmc_data = get_default_bmc_content()
    
    try:
        prompt_bmc = f"""En tant qu'expert en business model, analyse le projet suivant : {data}
        Génère un Business Model Canvas détaillé et pertinent sous forme de JSON strict.
        Retourne UNIQUEMENT le JSON suivant rempli avec un contenu pertinent (3-4-5 éléments par section et 3-4 mots par élément) :
        {{
            "partenaires_cles": [],
            "activites_cles": [],
            "ressources_cles": [],
            "proposition_valeur": [],
            "relation_client": [],
            "canaux": [],
            "segments_client": [],
            "structure_couts": [],
            "revenus": []
        }}"""

        response = generate_content(prompt_bmc)
        if hasattr(response, 'text'):
            json_str = response.text.strip()
        else:
            json_str = str(response).strip()
            
        # Extract JSON content
        json_start = json_str.find('{')
        json_end = json_str.rfind('}') + 1
        if json_start != -1 and json_end != -1:
            clean_json = json_str[json_start:json_end]
            parsed_data = json.loads(clean_json)
            if all(key in parsed_data for key in bmc_data.keys()):
                bmc_data = parsed_data
                print("✅ Contenu du Business Model Canvas généré avec succès !")
            else:
                raise ValueError("Structure JSON invalide")
        else:
            raise ValueError("JSON non trouvé dans la réponse")
            
    except Exception as e:
        print(f"⚠️ Erreur lors de la génération du contenu BMC: {str(e)}")
        print("🔄 Utilisation du contenu par défaut")

    # Get slide and add BMC sections
    slide = prs.slides[7]
    
    # Add BMC sections to slide
    add_bmc_text(slide, bmc_data["partenaires_cles"], 0, 1)
    add_bmc_text(slide, bmc_data["activites_cles"], 2, 1)
    add_bmc_text(slide, bmc_data["ressources_cles"], 2, 2.5)
    add_bmc_text(slide, bmc_data["proposition_valeur"], 4, 1)
    add_bmc_text(slide, bmc_data["relation_client"], 6, 1)
    add_bmc_text(slide, bmc_data["canaux"], 6, 2.5)
    add_bmc_text(slide, bmc_data["segments_client"], 8, 1)
    add_bmc_text(slide, bmc_data["structure_couts"], 0, 4, width=Inches(5), height=Inches(1.5))
    add_bmc_text(slide, bmc_data["revenus"], 5, 4, width=Inches(5), height=Inches(1.5))

def get_default_bmc_content():
    """Returns default BMC content if generation fails."""
    return {
        "partenaires_cles": ["Fournisseurs clés", "Partenaires stratégiques", "Distributeurs"],
        "activites_cles": ["Développement", "Marketing", "Support client"],
        "ressources_cles": ["Équipe technique", "Infrastructure", "Propriété intellectuelle"],
        "proposition_valeur": ["Innovation", "Qualité", "Service client"],
        "relation_client": ["Support dédié", "Communauté", "Formation"],
        "canaux": ["Web", "Mobile", "Partenaires"],
        "segments_client": ["Entreprises", "Particuliers", "Institutions"],
        "structure_couts": ["R&D", "Marketing", "Opérations"],
        "revenus": ["Abonnements", "Services", "Licences"]
    }

def add_bmc_text(slide, text_list, left, top, width=Inches(2), height=Inches(2)):
    """Add text to BMC sections with bullet points."""
    textbox = slide.shapes.add_textbox(Inches(left), Inches(top), width, height)
    text_frame = textbox.text_frame
    
    if text_frame.text:
        text_frame.clear()
    
    for i, item in enumerate(text_list):
        if i == 0:
            paragraph = text_frame.paragraphs[0]
        else:
            paragraph = text_frame.add_paragraph()
        paragraph.text = "• " + item
        
        for run in paragraph.runs:
            run.font.size = Pt(10)
            run.font.color.rgb = RGBColor(0, 0, 0)
            run.font.name = "Calibri"
