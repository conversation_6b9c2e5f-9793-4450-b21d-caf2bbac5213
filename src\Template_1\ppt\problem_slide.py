"""
Problem slide module.
Responsible for rendering the problem section of the pitch deck.
"""

from pptx.util import <PERSON><PERSON>, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from src.gemini_api import generate_content
from src.Template_1.text_utils import wrap_text

def add_problem_slide(prs, data):
    """
    Adds the problem content to the appropriate slide in the presentation.

    This function generates a concise problem statement for the project using
    Google's Gemini AI, formats it for readability, and then adds it to the
    problem slide in the PowerPoint presentation, along with a title.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, used as input for AI content generation.
    """
    # --- Generate Problem Text with Gemini AI ---
    # Define the prompt for Gemini AI to generate a succinct and impactful problem statement.
    # The prompt specifies a 3-sentence structure, no solutions, and clear readability.
    prompt_problem = (
        "En tant qu'expert dans la rédaction de pitch decks, "
        f"Rédige une problématique succincte et percutante pour le projet '{data}', "
        "Structure le texte en 3 phrases courtes, sans proposer de solutions, "
        "et assure-toi que le texte soit bien lisible avec des retours à la ligne pour une présentation claire."
    )
    # Generate content using the Gemini API.
    problem_raw = generate_content(prompt_problem)
    # Wrap the generated text to ensure it fits within the slide's layout, with a line limit of 8.
    problem_text = wrap_text(problem_raw, 8)

    # Access the specific slide intended for the problem statement.
    # In this template, it's assumed to be the 4th slide (index 3).
    slide = prs.slides[3]

    # --- Add the Title "Problème" to the Slide ---
    # Define the position and size for the title textbox.
    left, top, width, height = Inches(0.2), Inches(1.5), Inches(6), Inches(2)
    # Add the textbox shape for the title.
    title_box = slide.shapes.add_textbox(left, top, width, height)
    # Get the text frame of the title textbox.
    title_frame = title_box.text_frame
    # Set the title text.
    title_frame.text = "Problème"
    # Apply font formatting to all paragraphs and runs within the title text frame.
    for paragraph in title_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(40)  # Set font size.
            run.font.bold = True    # Set font to bold.
            run.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.

    # --- Add the Problem Content under the Title ---
    # Define the position and size for the content textbox.
    left, top, width, height = Inches(0.2), Inches(2.5), Inches(4), Inches(2)
    # Add the textbox shape for the content.
    content_box = slide.shapes.add_textbox(left, top, width, height)
    # Get the text frame of the content textbox.
    content_frame = content_box.text_frame
    # Set the problem content.
    content_frame.text = problem_text
    # Apply font formatting and alignment to all paragraphs and runs within the content text frame.
    for paragraph in content_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(15)  # Set font size.
            run.font.bold = False   # Set font to not bold.
            run.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.
        paragraph.alignment = PP_ALIGN.JUSTIFY # Justify align the text.
