"""
Finance slide module.
Responsible for rendering the finance/funding needs section of the pitch deck.
"""

from pptx.util import <PERSON>hes, Pt
from pptx.dml.color import RGBColor

def add_finance_slide(prs, data):
    """
    Adds the finance/funding needs content to the appropriate slide in the presentation.

    This function extracts funding-related data from the provided `data` dictionary,
    calculates the total financing, and then populates a table on the finance slide
    with these details. It handles different ways the funding data might be structured
    within the input `data`.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, containing financial information.
    """
    # --- Extract Funding Data ---
    ressources = {}
    # Attempt to extract 'ressources_data' from 'volume_vente' if it exists and is structured as expected.
    if "volume_vente" in data and isinstance(data["volume_vente"], list) and len(data["volume_vente"]) > 3:
        potential_ressources = data["volume_vente"][3]
        if isinstance(potential_ressources, dict) and "ressources_data" in potential_ressources:
            ressources = potential_ressources.get("ressources_data", {})
            print("✅ Extraction réussie des données de financement depuis 'volume_vente'.")
        else:
            # Fallback if 'volume_vente[3]' exists but doesn't contain 'ressources_data'.
            ressources = data.get("ressources_data", {})
            print("✅ Extraction réussie des données de financement directement depuis 'data' (volume_vente[3] did not contain ressources_data).")
    else:
        # Default extraction directly from 'data' if 'volume_vente' structure is not as expected.
        ressources = data.get("ressources_data", {})
        print("✅ Extraction réussie des données de financement directement depuis 'data'.")
            
    # Retrieve specific funding amounts, defaulting to 0 if not found.
    apport = ressources.get("Apports_des_associes", 0)
    pret_subvention = ressources.get("Prets_et_subvention", 0)
    financement_akkan = ressources.get("Crowdfunding_akkan", 0)
    # Calculate the total financing.
    total_financement = apport + pret_subvention + financement_akkan

    # Access the specific slide intended for finance/funding needs.
    # In this template, it's assumed to be the 14th slide (index 13).
    slide = prs.slides[13]

    # --- Add the Title "Besoin de financement" to the Slide ---
    # Define the position and size for the title textbox.
    left, top, width, height = Inches(1), Inches(0.5), Inches(8), Inches(1)
    # Add the textbox shape for the title.
    title_box = slide.shapes.add_textbox(left, top, width, height)
    # Get the text frame of the title textbox.
    title_frame = title_box.text_frame
    # Set the title text.
    title_frame.text = "Besoin de financement"
    # Apply font formatting to all paragraphs and runs within the title text frame.
    for paragraph in title_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(32)  # Set font size.
            run.font.bold = True    # Set font to bold.
            run.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.

    # --- Add a Table for Funding Data ---
    # Define the number of rows and columns for the table.
    rows, cols = 5, 2
    # Define the position and size for the table.
    left_table, top_table, width_table, height_table = Inches(1), Inches(1.5), Inches(6), Inches(2)
    # Add the table shape to the slide.
    table = slide.shapes.add_table(rows, cols, left_table, top_table, width_table, height_table).table

    # --- Fill the Table with Funding Data ---
    # Set header row texts.
    table.cell(0, 0).text = "Financement"
    table.cell(0, 1).text = "Montant en DH"
    # Populate rows with specific funding details and formatted amounts.
    table.cell(1, 0).text = "Apport des associés"
    table.cell(1, 1).text = f"{apport:,.2f} DH"
    table.cell(2, 0).text = "Prêts et subventions"
    table.cell(2, 1).text = f"{pret_subvention:,.2f} DH"
    table.cell(3, 0).text = "Reste à financer par AKKAN"
    table.cell(3, 1).text = f"{financement_akkan:,.2f} DH"
    table.cell(4, 0).text = "Total des financements"
    table.cell(4, 1).text = f"{total_financement:,.2f} DH"

    # --- Style the Table Cells ---
    # Iterate through each row and cell to apply consistent styling.
    for row in table.rows:
        for cell in row.cells:
            cell.fill.solid() # Set cell fill to solid.
            cell.fill.fore_color.rgb = RGBColor(0, 0, 0) # Set cell background color to black.
            # Apply font formatting to all paragraphs and runs within the cell's text frame.
            for paragraph in cell.text_frame.paragraphs:
                for run in paragraph.runs:
                    run.font.size = Pt(16)  # Set font size.
                    run.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.
