"""
SWOT slide module.
Responsible for rendering the SWOT analysis section of the pitch deck.
"""

import json
from pptx.util import <PERSON><PERSON>, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from src.gemini_api import generate_content

def add_swot_slide(prs, data):
    """
    Adds the SWOT analysis content to the appropriate slide in the presentation.

    This function generates a concise SWOT (Strengths, Weaknesses, Opportunities, Threats)
    analysis using Google's Gemini AI, parses the AI's JSON response, and then populates
    the SWOT slide in the PowerPoint presentation with the generated data. If the AI
    response is invalid, it falls back to default "Point non disponible" data.

    Args:
        prs (pptx.Presentation): The pptx.Presentation object representing the PowerPoint.
        data (dict): The loaded project data dictionary, used as input for AI content generation.
    """
    # --- Generate SWOT Analysis with Gemini AI ---
    # Define the prompt for Gemini AI to generate a concise SWOT analysis in JSON format.
    # The prompt specifies strict JSON output, maximum word count per point, and directness.
    prompt_swot = (
        "Analyse le projet suivant et génère une analyse SWOT très concise: {data}\n"
        "IMPORTANT:\n"
        "1. Ta réponse doit être UNIQUEMENT un objet JSON valide\n"
        "2. Chaque point DOIT faire maximum 5-6 mots\n"
        "3. Sois direct et précis\n"
        "4. Pas de phrases, juste des points clés\n"
        "Format exact à suivre:\n"
        "{{\n"
        '    "forces": ["Premier point fort", "Deuxième point fort", "Troisième point fort"],\n'
        '    "faiblesses": ["Première faiblesse", "Deuxième faiblesse", "Troisième faiblesse"],\n'
        '    "opportunites": ["Première opportunité", "Deuxième opportunité", "Troisième opportunité"],\n'
        '    "menaces": ["Première menace", "Deuxième menace", "Troisième menace"]\n'
        "}}"
    ).format(data=data)
    # Generate content using the Gemini API.
    swot_raw = generate_content(prompt_swot)
    # Strip any leading/trailing whitespace from the raw AI response.
    swot_text = swot_raw.strip()

    # --- Parse JSON Response ---
    try:
        # Find the start and end of the JSON object within the AI's response.
        json_start = swot_text.find('{')
        json_end = swot_text.rfind('}') + 1
        if json_start != -1 and json_end != -1:
            # Extract the clean JSON string.
            clean_json = swot_text[json_start:json_end]
            # Load the JSON string into a Python dictionary.
            swot_data = json.loads(clean_json)
        else:
            # If JSON markers are not found, raise an error.
            raise ValueError("JSON non trouvé dans la réponse")
    except Exception:
        # If parsing fails or JSON is not found, use default data.
        # This ensures the slide can still be generated even if AI response is problematic.
        swot_data = {
            "forces": ["Point non disponible"] * 3,
            "faiblesses": ["Point non disponible"] * 3,
            "opportunites": ["Point non disponible"] * 3,
            "menaces": ["Point non disponible"] * 3,
        }

    # Helper function to format a list of points into a bulleted string.
    def format_points(points):
        """
        Formats a list of strings into a bulleted list, taking up to the first 3 points.

        Args:
            points (list): A list of strings to be formatted.

        Returns:
            str: A string with each point prefixed by a bullet and separated by newlines.
        """
        return '\n'.join(f"- {point}" for point in points[:3])

    # Format the points for each SWOT section.
    strengths = format_points(swot_data.get('forces', []))
    weaknesses = format_points(swot_data.get('faiblesses', []))
    opportunities = format_points(swot_data.get('opportunites', []))
    threats = format_points(swot_data.get('menaces', []))

    # Access the specific slide intended for the SWOT analysis.
    # In this template, it's assumed to be the 6th slide (index 5).
    slide = prs.slides[5]

    # Helper function to add a SWOT section (title and content) to the slide.
    def add_swot_section(slide, title, content, left, top):
        """
        Adds a textbox with a title and content for a SWOT section to the slide.

        Args:
            slide (pptx.slide.Slide): The slide object to which the text will be added.
            title (str): The title of the SWOT section (e.g., "Forces").
            content (str): The text content (e.g., bulleted list of points).
            left (float): The x-coordinate (in inches) for the left edge of the textbox.
            top (float): The y-coordinate (in inches) for the top edge of the textbox.
        """
        # Add a textbox shape to the slide at the specified position and size.
        text_box = slide.shapes.add_textbox(Inches(left), Inches(top), Inches(3), Inches(2))
        text_frame = text_box.text_frame
        
        # Add the title paragraph.
        p = text_frame.add_paragraph()
        p.text = title
        p.font.size = Pt(20)  # Set font size.
        p.font.bold = True    # Set font to bold.
        p.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.
        
        # Add the content paragraph.
        p = text_frame.add_paragraph()
        p.text = content
        p.font.size = Pt(12)  # Set font size.
        p.font.color.rgb = RGBColor(255, 255, 255) # Set font color to white.
        p.alignment = PP_ALIGN.LEFT # Align text to the left.

    # Add the four SWOT sections to the slide at their respective positions.
    add_swot_section(slide, "Forces", strengths, 0.5, 1.5)
    add_swot_section(slide, "Faiblesses", weaknesses, 6.5, 1.5)
    add_swot_section(slide, "Opportunités", opportunities, 0.5, 2.8)
    add_swot_section(slide, "Menaces", threats, 6.5, 2.8)
