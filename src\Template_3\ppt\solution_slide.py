"""
Solution slide module.
Handles the creation and formatting of the solution slide.
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from src.gemini_api import generate_content

def create_solution_slide(prs, data):
    """
    Creates the solution slide.
    
    Args:
        prs: PowerPoint presentation object
        data: Dictionary containing project data
    """
    # Generate solution text using Gemini
    prompt_solution = (
        "En tant qu'expert dans la rédaction de pitch decks, "
        f"Rédige une solution concise et percutante pour le projet '{data}', "
        "en exposant clairement les approches innovantes et les bénéfices de cette solution, sans entrer dans les détails techniques. "
        "Structure le texte en 3 phrases courtes, en utilisant des retours à la ligne pour la lisibilité."
    )
    
    solution_text = generate_content(prompt_solution)
    wrapped_text = wrap_text(solution_text, 10)

    # Access the fifth slide (index 4)
    slide = prs.slides[4]
    
    # Add text box with solution text
    left_content = Inches(2)
    top_content = Inches(1.5)
    width_content = Inches(6)
    height_content = Inches(4)
    
    text_box = slide.shapes.add_textbox(left_content, top_content, width_content, height_content)
    text_frame = text_box.text_frame
    text_frame.text = wrapped_text
    
    # Format text
    for paragraph in text_frame.paragraphs:
        paragraph.alignment = PP_ALIGN.CENTER
        for run in paragraph.runs:
            run.font.size = Pt(14)
            run.font.bold = False
            run.font.color.rgb = RGBColor(0, 0, 0)

def wrap_text(text, words_per_line=8):
    """
    Wraps text to specified words per line.
    """
    words = text.split()
    lines = [" ".join(words[i:i + words_per_line]) for i in range(0, len(words), words_per_line)]
    return "\n".join(lines)
